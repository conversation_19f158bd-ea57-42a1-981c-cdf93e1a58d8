<form (ngSubmit)="onFilter()" #filterForm="ngForm" class="px-7 py-5" [ngClass]="{'show': isDropdownOpen}"
      [style.direction]="isRTL() ? 'rtl' : 'ltr'">
    <div class="fs-5 text-gray-900 fw-bolder" [style.text-align]="isRTL() ? 'right' : 'left'">
        {{ getTranslatedText('FILTER_OPTIONS') }}
    </div>
    <div class="separator border-gray-200 my-4"></div>

    <div class="mb-10">
        <label class="form-label fw-bold" [style.text-align]="isRTL() ? 'right' : 'left'">
            {{ getTranslatedText('SPECIALIZATION_SCOPE') }}:
        </label>
        <div>
            <select class="form-select form-select-solid" name="specializationScope"
                [(ngModel)]="selectedSpecializationScope"
                [attr.data-placeholder]="getTranslatedText('SELECT_OPTION')"
                [attr.aria-label]="getTranslatedText('SPECIALIZATION_SCOPE')"
                [style.direction]="isRTL() ? 'rtl' : 'ltr'"
                [style.text-align]="isRTL() ? 'right' : 'left'">
                <option [ngValue]="null">{{ getTranslatedText('SELECT') }}</option>
                <option *ngFor="let option of specializationScope" [value]="option.value">
                    {{ getTranslatedOptionText(option.key) }}
                </option>
            </select>
        </div>
    </div>

    <div class="d-flex" [class.justify-content-start]="isRTL()" [class.justify-content-end]="!isRTL()">
        <button type="reset" class="btn btn-sm btn-light btn-active-light-dark-blue"
                [class.me-2]="!isRTL()" [class.ms-2]="isRTL()"
                [attr.aria-label]="getTranslatedText('RESET')"
                (click)="onReset()">
            {{ getTranslatedText('RESET') }}
        </button>
        <button type="submit" class="btn btn-sm btn-dark-blue"
                [attr.aria-label]="getTranslatedText('APPLY')">
            {{ getTranslatedText('APPLY') }}
        </button>
    </div>
</form>