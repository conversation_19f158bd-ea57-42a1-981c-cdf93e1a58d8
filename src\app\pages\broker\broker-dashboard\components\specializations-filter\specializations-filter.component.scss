.menu-sub-dropdown {
  display: none;
  position: absolute;
  z-index: 9999 !important;
  background-color: #fff;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border-radius: 8px;
  border: 1px solid #e9ecef;
  min-width: 280px;
  max-width: 320px;

  &.show {
    display: block !important;
    position: absolute !important;
  }
}

// RTL Support
:host-context(html[dir="rtl"]),
:host-context(html[lang="ar"]) {
  .form-label {
    text-align: right !important;
    font-family: 'Noto Kufi Arabic', sans-serif !important;
  }

  .form-select {
    direction: rtl !important;
    text-align: right !important;
    font-family: 'Markazi Text', sans-serif !important;
  }

  .btn {
    font-family: 'Noto Kufi Arabic', sans-serif !important;
  }

  .fs-5 {
    font-family: 'Noto Kufi Arabic', sans-serif !important;
  }
}

// LTR Support
:host-context(html[dir="ltr"]),
:host-context(html[lang="en"]) {
  .form-label {
    text-align: left !important;
    font-family: 'Inter', sans-serif !important;
  }

  .form-select {
    direction: ltr !important;
    text-align: left !important;
    font-family: 'Inter', sans-serif !important;
  }

  .btn {
    font-family: 'Inter', sans-serif !important;
  }

  .fs-5 {
    font-family: 'Inter', sans-serif !important;
  }
}

// Dropdown positioning
:host {
  position: relative;
  display: inline-block;
}

.menu-sub-dropdown {
  position: absolute !important;
  top: calc(100% + 5px) !important;
  right: 0 !important;
  left: auto !important;
  margin: 0 !important;
  transform: none !important;

  &.show {
    animation: fadeIn 0.2s ease-in-out;
  }
}

// RTL positioning - dropdown opens to the left
:host-context(html[dir="rtl"]),
:host-context(html[lang="ar"]) {
  .menu-sub-dropdown {
    right: auto !important;
    left: 0 !important;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Form styling improvements
.form-select {
  &:focus {
    border-color: #000c66;
    box-shadow: 0 0 0 0.2rem rgba(0, 12, 102, 0.25);
  }
}

.btn-dark-blue {
  background-color: #000c66;
  border-color: #000c66;

  &:hover {
    background-color: #000a5c;
    border-color: #000a5c;
  }
}

// Responsive design
@media (max-width: 768px) {
  .menu-sub-dropdown {
    min-width: 250px;
    max-width: 280px;
    // Center the dropdown on mobile
    left: 50% !important;
    right: auto !important;
    transform: translateX(-50%) !important;
  }

  :host-context(html[dir="rtl"]),
  :host-context(html[lang="ar"]) {
    .menu-sub-dropdown {
      left: 50% !important;
      right: auto !important;
      transform: translateX(-50%) !important;
    }
  }
}

@media (max-width: 480px) {
  .menu-sub-dropdown {
    min-width: 220px;
    max-width: 250px;
    // Ensure it stays centered on very small screens
    left: 50% !important;
    right: auto !important;
    transform: translateX(-50%) !important;

    .px-7 {
      padding-left: 1rem !important;
      padding-right: 1rem !important;
    }
  }

  :host-context(html[dir="rtl"]),
  :host-context(html[lang="ar"]) {
    .menu-sub-dropdown {
      left: 50% !important;
      right: auto !important;
      transform: translateX(-50%) !important;
    }
  }
}
