import { Component, HostBinding, OnInit, Output, EventEmitter, ViewChild, OnDestroy } from '@angular/core';
import { NgForm } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-specializations-filter',
  templateUrl: './specializations-filter.component.html',
  styleUrls: ['./specializations-filter.component.scss']
})
export class SpecializationsFilterComponent implements OnInit, OnDestroy {
  @HostBinding('class') class = 'menu menu-sub menu-sub-dropdown w-250px w-md-300px';
  @Output() filterChanged = new EventEmitter<{ specializationScope: string | null }>();
  @ViewChild('filterForm') filterForm: NgForm;

  isDropdownOpen = false; // Control dropdown visibility
  selectedSpecializationScope: string | null = null;
  private langChangeSubscription: Subscription;

  specializationScope: { key: string; value: string }[] = [
    { key: 'Purchase/Sell Outside Compound', value: 'purchase_sell_outside_compound' },
    // { key: 'Purchase/Sell Inside Compound', value: 'purchase_sell_inside_compound' },
    { key: 'Primary Inside Compound', value: 'primary_inside_compound' },
    { key: 'Resale Inside Compound', value: 'resale_inside_compound' },
    { key: 'Rentals Outside Compound', value: 'rentals_outside_compound' },
    { key: 'Rentals Inside Compound', value: 'rentals_inside_compound' }
  ];

  constructor(private translate: TranslateService) {}

  ngOnInit(): void {
    // Subscribe to language changes
    this.langChangeSubscription = this.translate.onLangChange.subscribe(() => {
      // Force change detection when language changes
    });
  }

  ngOnDestroy(): void {
    if (this.langChangeSubscription) {
      this.langChangeSubscription.unsubscribe();
    }
  }

  isRTL(): boolean {
    return this.translate.currentLang === 'ar';
  }

  toggleDropdown(): void {
    this.isDropdownOpen = !this.isDropdownOpen;
    console.log('Dropdown toggled:', this.isDropdownOpen);

    // Close dropdown when clicking outside
    if (this.isDropdownOpen) {
      setTimeout(() => {
        document.addEventListener('click', this.closeDropdownOnOutsideClick.bind(this));
      }, 100);
    } else {
      document.removeEventListener('click', this.closeDropdownOnOutsideClick.bind(this));
    }
  }

  private closeDropdownOnOutsideClick(event: Event): void {
    const target = event.target as HTMLElement;
    const dropdown = document.querySelector('app-specializations-filter');

    if (dropdown && !dropdown.contains(target)) {
      this.isDropdownOpen = false;
      document.removeEventListener('click', this.closeDropdownOnOutsideClick.bind(this));
    }
  }

  onFilter(): void {
    const payload = {
      specializationScope: this.selectedSpecializationScope
    };
    this.filterChanged.emit(payload);
    this.isDropdownOpen = false; // Close dropdown on apply
  }

  onReset(): void {
    this.selectedSpecializationScope = null;
    this.filterForm.resetForm();
    this.filterChanged.emit({ specializationScope: null });
    this.isDropdownOpen = false; // Close dropdown on reset
  }

  getTranslatedText(key: string): string {
    const translations: { [key: string]: { ar: string; en: string } } = {
      'FILTER_OPTIONS': {
        ar: 'خيارات التصفية',
        en: 'Filter Options'
      },
      'SPECIALIZATION_SCOPE': {
        ar: 'نطاق التخصص',
        en: 'Specialization Scope'
      },
      'SELECT_OPTION': {
        ar: 'اختر خيار',
        en: 'Select option'
      },
      'SELECT': {
        ar: 'اختر',
        en: 'Select'
      },
      'RESET': {
        ar: 'إعادة تعيين',
        en: 'Reset'
      },
      'APPLY': {
        ar: 'تطبيق',
        en: 'Apply'
      }
    };

    const currentLang = this.translate.currentLang || 'en';
    return translations[key]?.[currentLang as 'ar' | 'en'] || key;
  }

  getTranslatedOptionText(key: string): string {
    const optionTranslations: { [key: string]: { ar: string; en: string } } = {
      'Purchase/Sell Outside Compound': {
        ar: 'شراء/بيع خارج المجمع',
        en: 'Purchase/Sell Outside Compound'
      },
      'Primary Inside Compound': {
        ar: 'أولي داخل المجمع',
        en: 'Primary Inside Compound'
      },
      'Resale Inside Compound': {
        ar: 'إعادة بيع داخل المجمع',
        en: 'Resale Inside Compound'
      },
      'Rentals Outside Compound': {
        ar: 'إيجارات خارج المجمع',
        en: 'Rentals Outside Compound'
      },
      'Rentals Inside Compound': {
        ar: 'إيجارات داخل المجمع',
        en: 'Rentals Inside Compound'
      }
    };

    const currentLang = this.translate.currentLang || 'en';
    return optionTranslations[key]?.[currentLang as 'ar' | 'en'] || key;
  }
}
