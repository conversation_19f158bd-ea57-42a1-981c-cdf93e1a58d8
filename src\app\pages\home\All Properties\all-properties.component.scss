// All Properties Component Styles

// Exact Filter Design from Image
.exact-filter-bar {
  margin-bottom: 2rem;
  background-image: url("~src/assets/media/home/<USER>");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  width: 100%;
  // margin-left: 5%;
  position: relative;
  padding: 60px 0;
  border-radius: 0;

  // Dark blue overlay
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(5, 54, 128, 0.9),
      rgba(4, 56, 107, 0.8)
    );
    z-index: 1;
  }

  .container {
    position: relative;
    z-index: 2;
  }

  .filter-card {
    background: white;
    border-radius: 20px;
    padding: 20px 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    max-width: 900px;
    margin: 0 auto;
  }

  // Top Row
  .top-row {
    display: flex;
    align-items: center;
    gap: 50px;
    margin-bottom: 20px;
    flex-wrap: wrap;

    .rent-dropdown,
    .house-dropdown {
      position: relative;

      .dropdown-btn {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 10px 15px;
        font-size: 14px;
        color: #495057;
        display: flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
        transition: all 0.2s ease;
        white-space: nowrap;

        &:hover {
          background: #e9ecef;
          border-color: #007bff;
        }

        i {
          font-size: 12px;
          color: #6c757d;
        }

        .fas.fa-chevron-down {
          font-size: 10px;
          margin-left: 5px;
        }
      }

      .dropdown-menu-exact {
        position: absolute;
        top: 100%;
        left: 0;
        z-index: 1000;
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        padding: 5px 0;
        min-width: 150px;
        opacity: 0;
        visibility: hidden;
        transform: translateY(-10px);
        transition: all 0.2s ease;

        &.show {
          opacity: 1;
          visibility: visible;
          transform: translateY(0);
        }

        .dropdown-item-exact {
          padding: 8px 15px;
          font-size: 14px;
          color: #495057;
          cursor: pointer;
          transition: background 0.2s ease;
          display: block;
          text-decoration: none;

          &:hover {
            background: #f8f9fa;
            color: #007bff;
          }
        }
      }
    }

    .location-input-exact,
    .area-dropdown-exact,
    .unit-type-dropdown-exact {
      flex: 0 0 auto;
      position: relative;
      // min-width: 250px;
      // max-width: 129px;
      margin-left: 10px;

      .location-field {
        width: 100%;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 12px 15px;
        font-size: 14px;
        height: 45px;
        font-weight: 600;
        background: #f8f9fa;
        transition: all 0.2s ease;
        height: 45px;

        &:focus {
          outline: none;
          border-color: #007bff;
          background: white;
          box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        &::placeholder {
          color: #6c757d;
          font-size: 14px;
          font-weight: 600;
        }
      }

      .clear-btn-exact {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: #6c757d;
        cursor: pointer;
        padding: 5px;

        &:hover {
          color: #495057;
        }
      }
    }

    // Area Dropdown & Unit Type Dropdown Styling
    .area-dropdown-exact,
    .unit-type-dropdown-exact {
      .dropdown {
        width: 100%;
      }

      .area-btn-exact,
      .unit-type-btn-exact {
        width: 129px;
        height: 45px;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 12px 15px;
        font-size: 14px;
        background: #f8f9fa;
        color: #495057;
        text-align: left;
        display: flex;
        justify-content: space-between;
        align-items: center;
        transition: all 0.2s ease;
        opacity: 1;
        position: relative;

        &:hover {
          border-color: #007bff;
          background: #fff;
          box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
        }

        &.active {
          border-color: #007bff;
          background: #fff;
          box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
        }

        .fas {
          transition: transform 0.2s ease;

          &.rotated {
            transform: rotate(180deg);
          }
        }

        span {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 90px;
        }
      }

      .dropdown-menu {
        min-width: 180px;
        border: 1px solid #e9ecef;
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        max-height: 200px;
        overflow-y: auto;
        margin-top: 5px;
        left: 0 !important;
        transform: none !important;

        .dropdown-item {
          padding: 8px 15px;
          font-size: 14px;
          color: #495057;
          transition: all 0.2s ease;
          white-space: nowrap;

          &:hover {
            background: #f8f9fa;
            color: #007bff;
          }

          &:active {
            background: #007bff;
            color: white;
          }
        }
      }
    }
  }
  @media (max-width: 768px) and (min-width: 576px) {
    .top-row {
        margin-bottom: 60px !important;
    }
  }
  // Bottom Row
  .bottom-row {
    display: flex;
    align-items: center;
    gap: 30px;
    flex-wrap: wrap;

    .price-label-exact,
    .area-label-exact {
      font-size: 14px;
      font-weight: 600;
      color: #495057;
      white-space: nowrap;
    }

    .price-slider-exact,
    .area-slider-exact {
      flex: 0;
      min-width: 246px;

      .slider-track {
        position: relative;
        height: 40px;
        display: flex;
        align-items: center;
        cursor: pointer;

        .slider-line {
          width: 95%;
          height: 4px;
          background: #e9ecef;
          border-radius: 2px;
          position: relative;
        }
        @media (max-width: 425px) {
          .slider-line {
          width: 100%;
          }
        }

        .slider-range {
          position: absolute;
          height: 4px;
          background: #007bff;
          border-radius: 2px;
          top: 50%;
          transform: translateY(-50%);
        }

        .slider-handle {
          position: absolute;
          width: 16px;
          height: 16px;
          background: #007bff;
          border: 3px solid white;
          border-radius: 50%;
          cursor: pointer;
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
          z-index: 2;
          top: 50%;
          transform: translate(-50%, -50%);
          user-select: none;
          transition: all 0.2s ease;

          &:hover {
            transform: translate(-50%, -50%) scale(1.2);
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
          }

          &:active {
            transform: translate(-50%, -50%) scale(1.1);
          }

          .price-bubble,
          .area-bubble {
            position: absolute;
            bottom: 25px;
            left: 50%;
            transform: translateX(-50%);
            background: #007bff;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
            white-space: nowrap;

            &::after {
              content: "";
              position: absolute;
              top: 100%;
              left: 50%;
              transform: translateX(-50%);
              border: 4px solid transparent;
              border-top-color: #007bff;
            }
          }

          .area-bubble {
            background: #28a745;

            &::after {
              border-top-color: #28a745;
            }
          }
        }
      }

      .range-input {
        position: absolute;
        width: 100%;
        height: 4px;
        background: transparent;
        outline: none;
        opacity: 0;
        pointer-events: none;
        top: 50%;
        transform: translateY(-50%);
      }
    }

    .search-btn-exact {
      margin-left: auto;
      position: relative;
      padding-left: 30px;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
      margin-top: -50px;

      &::before {
        content: "";
        position: absolute;
        left: -12px;
        top: 40%;
        transform: translateY(-50%);
        width: 3px;
        height: 100px;
        background: #dee2e6;
      }

      .total-search-label {
        font-size: 12px;
        font-weight: 500;
        color: #6c757d;
        text-align: center;
        margin-bottom: 0;
      }

      .search-button-exact {
        background: #e9ecef;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 12px 15px;
        font-size: 14px;
        height: 45px;
        font-weight: 600;
        color: #495057;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          background: #dee2e6;
          color: #343a40;
        }
      }
    }
  }

  // RTL Support
  :host-context(html[dir="rtl"]) & {
    .top-row {
      direction: rtl;

      .location-input-exact {
        min-width: 320px;

        .location-field {
          text-align: right;
          font-family: "Noto Kufi Arabic", sans-serif;
          font-size: 14px;
          font-weight: 600;
          padding: 12px 15px;
          height: 45px;

          &::placeholder {
            font-family: "Noto Kufi Arabic", sans-serif;
            font-size: 14px;
            font-weight: 600;
          }
        }

        .clear-btn-exact {
          right: auto;
          left: 10px;
        }
      }

      .area-dropdown-exact,
      .unit-type-dropdown-exact {
        .area-btn-exact,
        .unit-type-btn-exact {
          text-align: right;
          font-family: "Noto Kufi Arabic", sans-serif;
          font-size: 14px;
          font-weight: 600;
          padding: 12px 15px;
          height: 45px;
          // flex-direction: row-reverse;
        }

        .dropdown-menu {
          right: 0;
          left: auto;

          .dropdown-item {
            text-align: right;
            font-family: "Noto Kufi Arabic", sans-serif;
          }
        }
      }
    }

    .bottom-row {
      direction: rtl;

      .price-label-exact,
      .area-label-exact {
        font-family: "Noto Kufi Arabic", sans-serif;
      }

      .price-slider-exact,
      .area-slider-exact {
        .price-bubble,
        .area-bubble {
          font-family: "Noto Kufi Arabic", sans-serif;
          direction: rtl;
          text-align: center;
        }
      }

      .search-btn-exact {
        margin-left: 0;
        margin-right: auto;
        padding-left: 0;
        padding-right: 30px;
        margin-top: -50px;

        &::before {
          left: auto;
          right: -12px;
        }

        .total-search-label {
          font-family: "Noto Kufi Arabic", sans-serif;
        }

        .search-button-exact {
          font-family: "Noto Kufi Arabic", sans-serif;
          font-size: 14px;
          font-weight: 600;
          padding: 12px 15px;
          height: 45px;
        }
      }
    }
  }

  // Responsive Design
  @media (max-width: 768px) {
    padding: 40px 0;

    .filter-card {
      padding: 15px 20px;
      margin: 0 15px;
    }

    .top-row,
    .bottom-row {
      gap: 10px;
    }
    @media (max-width: 768px) {
      .top-row
      {
        gap: 33px !important;
      }
      .bottom-row
      {
        gap: 10px !important;
      }
    }
    @media (max-width: 576px) {
      .bottom-row{
        text-align: center !important;
        flex-direction: column !important;
        align-items: stretch !important;
      }
      .area-slider-exact{
        width:100% !important;
      }
      .search-btn-exact{
        margin: 3px auto !important ;
      }
    }

    // RTL Mobile Support
    :host-context(html[dir="rtl"]) & {
      .top-row,
      .bottom-row {
        direction: rtl;
      }
    }

    .dropdown-btn {
      padding: 8px 12px !important;
      font-size: 13px !important;
    }

    .location-input-exact,
    .area-dropdown-exact,
    .unit-type-dropdown-exact {
      min-width: 129px !important;
      max-width: 129px !important;

      .location-field {
        font-size: 14px !important;
        font-weight: 600 !important;
        padding: 12px 15px !important;
        height: 45px !important;

        &::placeholder {
          font-size: 14px !important;
          font-weight: 600 !important;
        }
      }

      .area-btn-exact,
      .unit-type-btn-exact {
        width: 129px;
        height: 45px;
        font-size: 14px;
        padding: 12px 15px;

        span {
          max-width: 80px;
        }
      }
    }

    @media (max-width: 768px) {
      .location-input-exact,
      .area-dropdown-exact,
      .unit-type-dropdown-exact {
        margin-bottom: -20px;
      }
    }

    .price-slider-exact,
    .area-slider-exact {
      min-width: 150px !important;
    }
  }
}

// Old Compact Filter Bar (keeping for compatibility)
.compact-filter-bar {
  margin-bottom: 1.5rem;
  background-image: url("/assets/media/home/<USER>");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  padding: 40px 0;
  border-radius: 16px;
  overflow: hidden;

  // Dark blue overlay
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #232176;
    z-index: 1;
  }

  .container {
    position: relative;
    z-index: 2;
  }

  // Search Title Styling
  .search-title {
    .title-text {
      color: white;
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 0.5rem;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      line-height: 1.2;
    }

    .subtitle-text {
      color: rgba(255, 255, 255, 0.9);
      font-size: 1.1rem;
      font-weight: 400;
      margin-bottom: 0;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    }
  }

  .filter-wrapper {
    background: rgba(255, 255, 255, 0.98);
    border-radius: 16px;
    padding: 20px 25px;
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.25);
    border: 1px solid rgba(255, 255, 255, 0.4);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
  }

  .filter-item {
    .dropdown {
      position: relative;

      .filter-btn {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 8px 12px;
        font-size: 0.85rem;
        color: #495057;
        display: flex;
        align-items: center;
        gap: 6px;
        white-space: nowrap;
        transition: all 0.2s ease;
        cursor: pointer;
        min-width: 100px;

        &:hover {
          background: #e9ecef;
          border-color: #007bff;
        }

        i {
          font-size: 0.8rem;
          color: #6c757d;
        }

        .fas.fa-chevron-down {
          font-size: 0.7rem;
          margin-left: auto;
        }
      }

      .dropdown-menu {
        position: absolute;
        top: 100%;
        left: 0;
        z-index: 1000;
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        padding: 5px 0;
        min-width: 150px;
        opacity: 0;
        visibility: hidden;
        transform: translateY(-10px);
        transition: all 0.2s ease;

        &.show {
          opacity: 1;
          visibility: visible;
          transform: translateY(0);
        }

        .dropdown-item {
          padding: 8px 15px;
          font-size: 0.85rem;
          color: #495057;
          cursor: pointer;
          transition: background 0.2s ease;

          &:hover {
            background: #f8f9fa;
            color: #007bff;
          }
        }
      }

      // Price dropdown special styling
      &.price-dropdown {
        .dropdown-menu {
          min-width: 250px;
        }
      }
    }

    .location-search {
      position: relative;
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      padding: 8px 12px;
      display: flex;
      align-items: center;
      gap: 8px;
      transition: all 0.2s ease;

      &:focus-within {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
      }

      i {
        color: #6c757d;
        font-size: 0.8rem;
      }

      .location-input {
        border: none;
        background: transparent;
        outline: none;
        flex: 1;
        font-size: 0.85rem;
        color: #495057;

        &::placeholder {
          color: #6c757d;
        }
      }

      .clear-btn {
        background: none;
        border: none;
        color: #6c757d;
        cursor: pointer;
        padding: 2px;
        border-radius: 50%;
        transition: all 0.2s ease;

        &:hover {
          background: #e9ecef;
          color: #495057;
        }

        i {
          font-size: 0.7rem;
        }
      }
    }
  }

  .search-button {
    background: #007bff;
    border: none;
    border-radius: 8px;
    padding: 8px 16px;
    color: white;
    font-size: 0.85rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.2s ease;
    cursor: pointer;

    &:hover {
      background: #0056b3;
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
    }

    i {
      font-size: 0.8rem;
    }
  }

  .results-count {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(233, 236, 239, 0.5);
    border-radius: 8px;
    padding: 8px 12px;
    text-align: center;
    min-width: 70px;
    backdrop-filter: blur(5px);

    .count {
      display: block;
      font-size: 1rem;
      font-weight: 600;
      color: #007bff;
      line-height: 1;
    }

    .label {
      font-size: 0.7rem;
      color: #6c757d;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
  }

  // Price Slider in Dropdown
  .price-slider-wrapper {
    padding: 15px;

    .price-values {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
      font-size: 0.8rem;
      font-weight: 500;
      color: #495057;
    }

    .price-slider {
      position: relative;
      height: 20px;

      input[type="range"] {
        position: absolute;
        width: 100%;
        height: 4px;
        background: transparent;
        outline: none;
        -webkit-appearance: none;
        appearance: none;
        pointer-events: none;

        &::-webkit-slider-track {
          height: 4px;
          background: #e9ecef;
          border-radius: 2px;
        }

        &::-webkit-slider-thumb {
          -webkit-appearance: none;
          height: 16px;
          width: 16px;
          background: #007bff;
          border-radius: 50%;
          cursor: pointer;
          pointer-events: all;
          border: 2px solid white;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        &::-moz-range-track {
          height: 4px;
          background: #e9ecef;
          border-radius: 2px;
          border: none;
        }

        &::-moz-range-thumb {
          height: 16px;
          width: 16px;
          background: #007bff;
          border-radius: 50%;
          cursor: pointer;
          pointer-events: all;
          border: 2px solid white;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
      }
    }
  }

  // RTL Support
  :host-context(html[dir="rtl"]) & {
    .search-title {
      .title-text {
        font-family: "Noto Kufi Arabic", sans-serif;
        font-size: 2.2rem;
      }

      .subtitle-text {
        font-family: "Markazi Text", sans-serif;
        font-size: 1.2rem;
      }
    }

    .filter-item {
      .dropdown {
        .dropdown-menu {
          left: auto;
          right: 0;
        }
      }

      .location-search {
        .location-input {
          text-align: right;
          font-family: "Noto Kufi Arabic", sans-serif;
        }
      }
    }

    .filter-btn,
    .search-button,
    .dropdown-item {
      font-family: "Noto Kufi Arabic", sans-serif;
    }

    .results-count .label {
      font-family: "Noto Kufi Arabic", sans-serif;
    }
  }

  // Responsive Design
  @media (max-width: 768px) {
    padding: 30px 0;

    .search-title {
      .title-text {
        font-size: 1.8rem !important;
      }

      .subtitle-text {
        font-size: 0.95rem !important;
      }
    }

    .filter-wrapper {
      padding: 12px 15px;
    }

    .filter-btn {
      min-width: auto !important;
      padding: 6px 10px !important;
      font-size: 0.8rem !important;
    }

    .search-button {
      padding: 6px 12px !important;
      font-size: 0.8rem !important;
    }

    .results-count {
      min-width: 60px !important;
      padding: 6px 10px !important;
    }
  }

  @media (max-width: 576px) {
    .search-title {
      .title-text {
        font-size: 1.5rem !important;
      }

      .subtitle-text {
        font-size: 0.9rem !important;
      }
    }
  }
}

// Modern Filter Bar - Exact Design Match
.modern-filter-bar {
  margin-bottom: 2rem;
  padding: 0;

  .filter-container {
    background: white;
    border-radius: 20px;
    padding: 20px 25px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    border: 1px solid #e9ecef;

    .top-filter-row {
      margin-bottom: 15px;

      .filter-dropdown {
        .filter-btn {
          background: #f8f9fa;
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 10px 15px;
          font-size: 1.1rem;
          font-weight: 500;
          color: #495057;
          display: flex;
          align-items: center;
          white-space: nowrap;
          transition: all 0.3s ease;

          &:hover {
            background: #e9ecef;
            border-color: #dee2e6;
          }

          i {
            font-size: 0.8rem;
          }
        }

        .dropdown-menu {
          border-radius: 8px;
          border: 1px solid #e9ecef;
          box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);

          .dropdown-item {
            padding: 8px 15px;
            font-size: 0.9rem;
            transition: all 0.3s ease;

            &:hover {
              background: #f8f9fa;
            }
          }
        }
      }

      .location-input-container {
        position: relative;

        .location-input {
          width: 100%;
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 10px 15px;
          font-size: 0.9rem;
          background: #f8f9fa;
          transition: all 0.3s ease;

          &:focus {
            outline: none;
            border-color: #007bff;
            background: white;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
          }

          &::placeholder {
            color: #6c757d;
          }
        }

        .clear-location {
          position: absolute;
          right: 10px;
          top: 50%;
          transform: translateY(-50%);
          background: none;
          border: none;
          color: #6c757d;
          cursor: pointer;
          padding: 5px;

          &:hover {
            color: #495057;
          }
        }
      }

      .results-badge {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 8px 15px;
        display: flex;
        flex-direction: column;
        align-items: center;
        min-width: 80px;

        .results-number {
          font-size: 1.2rem;
          font-weight: 700;
          color: #495057;
          line-height: 1;
        }

        .results-text {
          font-size: 0.75rem;
          color: #6c757d;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }
      }

      .user-avatar {
        width: 40px;
        height: 40px;
        background: #dc3545;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;

        .avatar-letter {
          color: white;
          font-weight: 700;
          font-size: 1.1rem;
        }
      }
    }

    .bottom-filter-row {
      .price-label {
        font-size: 0.9rem;
        font-weight: 600;
        color: #495057;
        white-space: nowrap;
      }

      .price-slider-container {
        .price-range-display {
          position: relative;

          .price-track {
            position: relative;
            height: 6px;
            background: #e9ecef;
            border-radius: 3px;
            margin: 20px 0;
            cursor: pointer;

            .price-range-fill {
              position: absolute;
              height: 100%;
              background: #007bff;
              border-radius: 3px;
              transition: all 0.1s ease;
            }

            .price-handle {
              position: absolute;
              top: -8px;
              width: 20px;
              height: 20px;
              background: #007bff;
              border: 3px solid white;
              border-radius: 50%;
              cursor: grab;
              box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
              transition: all 0.1s ease;
              z-index: 2;

              &:hover {
                transform: scale(1.1);
                box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
              }

              &:active {
                cursor: grabbing;
                transform: scale(1.2);
              }

              .price-value {
                position: absolute;
                top: -35px;
                left: 50%;
                transform: translateX(-50%);
                background: #007bff;
                color: white;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 0.75rem;
                font-weight: 600;
                white-space: nowrap;
                pointer-events: none;

                &::after {
                  content: "";
                  position: absolute;
                  top: 100%;
                  left: 50%;
                  transform: translateX(-50%);
                  border: 4px solid transparent;
                  border-top-color: #007bff;
                }
              }
            }
          }

          // Hidden range inputs for accessibility and fallback
          .range-input {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            pointer-events: none;
            z-index: 1;

            &:focus {
              outline: none;
            }
          }
        }
      }

      .search-btn {
        background: #e9ecef;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 10px 20px;
        font-size: 0.9rem;
        font-weight: 600;
        color: #495057;
        transition: all 0.3s ease;

        &:hover {
          background: #dee2e6;
          color: #343a40;
        }
      }
    }
  }

  // RTL Support
  :host-context(html[dir="rtl"]) & {
    .filter-container {
      .top-filter-row {
        .location-input-container {
          .location-input {
            text-align: right;
            font-family: "Noto Kufi Arabic", sans-serif;
          }

          .clear-location {
            right: auto;
            left: 10px;
          }
        }

        .filter-dropdown {
          .filter-btn {
            font-family: "Noto Kufi Arabic", sans-serif;
          }
        }

        .results-badge {
          .results-text {
            font-family: "Noto Kufi Arabic", sans-serif;
          }
        }
      }

      .bottom-filter-row {
        .price-label {
          font-family: "Noto Kufi Arabic", sans-serif;
        }

        .search-btn {
          font-family: "Noto Kufi Arabic", sans-serif;
        }
      }
    }
  }
}

/* Header Styles - Copied from Home Page */
// Add padding to account for fixed navbar
.all-properties-page {
  background-color: #ffffff !important;
  padding-top: 70px; // Reduced padding for fixed navbar
  overflow-x: hidden; // Prevent horizontal scroll
  max-width: 100vw; // Ensure page doesn't exceed viewport width
}

// Home Header Styles
.home-header {
  position: relative;
  width: 100%;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.95);
  font-family: "Noto Kufi Arabic" !important;

  // Navigation Bar
  .navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    z-index: 1000;
    backdrop-filter: blur(10px);
    padding: 0.5rem 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    background: rgba(255, 255, 255, 0.568);
    min-height: 60px;
    overflow: visible !important;

    .container-fluid {
      max-width: 1400px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 1rem;
      box-sizing: border-box;
      width: 100%;
      overflow: visible !important;
    }

    .navbar-brand {
      .logo-img {
        height: 50px;
        width: auto;
        object-fit: contain;
      }
    }

    .nav-list {
      list-style: none;
      // gap: 2rem;
      margin: 0;
      padding: 0;

      .nav-item {
        .nav-link {
          color: #2c3e50;
          text-decoration: none;
          font-weight: 700;
          font-size: 1rem;
          padding: 0.5rem 1rem;
          border-radius: 8px;
          transition: all 0.3s ease;
          direction: rtl;
          font-family: "Noto Kufi Arabic" !important;

          &:hover {
            background-color: #f8f9fa;
            color: #27ae60;
            transform: translateY(-2px);
          }

          &.user-link {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            border-radius: 25px;
            padding: 0.7rem 1.5rem;

            &:hover {
              background: linear-gradient(135deg, #229954, #27ae60);
              transform: translateY(-2px);
              box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);
            }

            i {
              font-size: 0.9rem;
            }
          }
        }
      }
    }

    // User Registration Link (separate from nav list)
    .user-link {
      background: linear-gradient(135deg, #27ae60, #2ecc71);
      color: white !important;
      border-radius: 25px;
      padding: 0.5rem 1rem;
      text-decoration: none;
      font-weight: 500;
      font-size: 0.9rem;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      font-family: "Noto Kufi Arabic" !important;
      min-height: 38px;
      box-sizing: border-box;
      white-space: nowrap;

      &:hover {
        background: linear-gradient(135deg, #229954, #27ae60);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);
        color: white !important;
        text-decoration: none;
      }

      i {
        font-size: 0.9rem;
        margin-right: 0.5rem;
      }
    }

    // User Profile (when logged in)
    .user-profile {
      display: flex;
      align-items: center;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 25px;
      padding: 0.5rem 1rem;
      border: 1px solid rgba(250, 250, 250, 0.3);
      transition: all 0.3s ease;
      cursor: pointer;
      font-family: "Noto Kufi Arabic" !important;

      &:hover {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(39, 174, 96, 0.5);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(39, 174, 96, 0.2);
      }

      .user-avatar {
        width: 35px;
        height: 35px;
        border-radius: 50%;
        object-fit: cover;
        border: 2px solid #27ae60;
      }

      .user-name {
        font-weight: 600;
        color: #2c3e50;
        font-size: 1.4rem;
      }

      i.fa-chevron-down {
        font-size: 0.8rem;
        color: #27ae60;
        transition: transform 0.3s ease;
      }
    }
  }

    // User Dropdown Menu
    .user-dropdown {
      position: absolute;
      top: 100%;
      right: 0;
      background: white;
      border-radius: 10px;
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
      min-width: 220px;
      z-index: 1000;
      border: 1px solid rgba(0, 0, 0, 0.08);
      overflow: hidden;
      animation: dropdownFadeIn 0.3s ease;
      font-family: "Noto Kufi Arabic" !important;

      .dropdown-item {
        display: flex;
        align-items: center;
        padding: 10px 14px;
        cursor: pointer;
        transition: all 0.3s ease;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);

        &:hover {
          background-color: #f8f9fa;
          transform: translateX(-3px);
        }

        i,
        .menu-icon {
          width: 18px;
          text-align: center;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .menu-icon {
          svg {
            width: 16px;
            height: 16px;
          }
        }

        span {
          font-weight: 500;
          color: #2c3e50;
          font-size: 0.9rem;
        }

        &.logout-item {
          &:hover {
            background-color: #fff5f5;
          }

          span {
            color: #e74c3c;
          }
        }

        &.new-request-item {
          background: linear-gradient(135deg, #e8f5e8, #f0f8f0);
          border-top: 2px solid #27ae60;

          &:hover {
            background: linear-gradient(135deg, #d4f4d4, #e8f5e8);
          }

          span {
            color: #27ae60;
            font-weight: 600;
            text-align: center;
            width: 100%;
          }
        }
      }

      .dropdown-divider {
        height: 1px;
        background: rgba(0, 0, 0, 0.1);
        margin: 0;
      }
    }
  }


@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Mobile Navigation Styles
.navbar-toggler {
  border: none;
  background: transparent;
  padding: 8px 12px;
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(0, 123, 255, 0.1);
  }

  &:focus {
    box-shadow: none;
    outline: none;
  }

  .navbar-toggler-icon {
    display: flex;
    align-items: center;
    justify-content: center;

    i {
      font-size: 1.2rem;
      color: #333;
      transition: all 0.3s ease;
    }
  }
}

// Mobile Navigation Dropdown
.mobile-nav-dropdown {
  position: absolute;
  top: calc(100% + -1.8rem) !important;
  left: 50%;
  transform: translateX(-50%);
  margin-left: -25px !important;
  width: 200px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  z-index: 1040;
  border: 1px solid rgba(0, 0, 0, 0.08);
  overflow: visible;
  animation: dropdownFadeIn 0.3s ease;
  margin-top: 0;
  font-family: "Noto Kufi Arabic" !important;

  .dropdown-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    color: #333;
    text-decoration: none;
    transition: all 0.2s ease;
    cursor: pointer;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);

    &:last-child {
      border-bottom: none;
    }

    &:hover {
      background: rgba(0, 123, 255, 0.05);
      color: #007bff;
    }

    .menu-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 18px;
      height: 18px;

      i {
        font-size: 0.8rem;
      }
    }

    span {
      font-size: 0.8rem;
      font-weight: 500;
    }
  }
}

/* RTL Support for Property Cards */
:host-context(html[dir="rtl"]) {
  .property-card {
    .property-image {
      .property-top-badges {
        left: 16px;
        right: 16px;
        // flex-direction: row-reverse;

        .property-badge-featured {
          order: 2;
        }

        .property-price-overlay {
          order: 1;
        }
      }

      .property-type-badge {
        left: auto;
        right: 16px;
      }

      .property-title {
        left: 16px;
        right: 16px;
        text-align: right;
        // flex-direction: row-reverse;

        .verified-icon {
          margin-left: 0;
          margin-right: 8px;
        }
      }

      .property-location-text {
        left: 16px;
        right: 16px;
        text-align: right;
        // flex-direction: row-reverse;

        i {
          margin-left: 6px;
          margin-right: 0;
        }
      }
    }

    .property-content {
      .property-bottom-row {
        .property-rating {
          flex-direction: row-reverse;

          .stars {
            order: 3;
          }

          .rating-text {
            order: 2;
          }

          .reviews-count {
            order: 1;
          }
        }

        .property-actions {
          order: 1;
        }
      }
    }
  }

  // User Dropdown General Styling
  .navbar-nav {
    position: static !important;

    .user-dropdown {
      position: fixed !important;
      top: 70px !important;
      // right: 15px !important;
      left: 3% !important;
      z-index: 1050 !important;
      min-width: 200px !important;
      max-width: 300px !important;
      background: white !important;
      border: 1px solid rgba(0, 0, 0, 0.15) !important;
      border-radius: 8px !important;
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15) !important;
      overflow: visible !important;
      animation: dropdownFadeIn 0.3s ease !important;

      .dropdown-item {
        padding: 10px 15px !important;
        font-size: 0.9rem !important;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05) !important;
        transition: all 0.2s ease !important;

        &:last-child {
          border-bottom: none !important;
        }

        &:hover {
          background: rgba(0, 123, 255, 0.05) !important;
          color: #007bff !important;
        }

        .menu-icon {
          width: 18px !important;
          height: 18px !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;

          svg {
            width: 16px !important;
            height: 16px !important;
          }
        }
      }

      .dropdown-divider {
        margin: 4px 0 !important;
        border-top: 1px solid rgba(0, 0, 0, 0.08) !important;
      }
    }
  }

  // Dropdown overlay for mobile
  .dropdown-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    background: rgba(0, 0, 0, 0.1) !important;
    z-index: 1040 !important;
    display: block !important;
  }
}

/* LTR Support for Header - Compact Design */
:host-context(html[dir="ltr"]) {
  .home-header {
    .navbar {
      padding: 0.3rem 0;
      min-height: 55px;

      .nav-list {
        gap: 1.5rem;

        .nav-item {
          .nav-link {
            font-size: 0.9rem;
            padding: 0.3rem 0.8rem;
          }
        }
      }
    }
  }
}

/* RTL Support for Header */
:host-context(html[dir="rtl"]) {
  .home-header {
    .navbar {
      .nav-list {
        .nav-item {
          margin: 0 1rem 0 0;
        }
      }

      .user-profile {
        .user-avatar {
          margin-left: 0.5rem;
          margin-right: 0;
        }
      }
    }

    .mobile-nav-dropdown {
      .dropdown-item {
        .menu-icon {
          margin-left: 0.75rem;
          margin-right: 0;
        }
      }
    }

    .user-dropdown {
      left: 0;
      right: auto;

      .dropdown-item {
        .menu-icon {
          margin-left: 0.75rem;
          margin-right: 0;
        }
      }
    }
  }
}

/* Responsive Design for Header - Copied from Home Page */
@media (max-width: 768px) {
  .all-properties-page {
    padding-top: 70px; // Reduced padding for mobile
  }

  .home-header {
    .navbar {
      overflow: visible !important;

      .container-fluid {
        flex-direction: column;
        gap: 1rem;
        overflow: visible !important;
      }

      .nav-list {
        gap: 1rem;
        flex-direction: column;
        text-align: center;

        .nav-item {
          .nav-link {
            font-size: 0.9rem;
            padding: 0.4rem 0.8rem;
          }
        }
      }

      .user-link {
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
        min-height: 38px;
        box-sizing: border-box;
      }

      .user-profile {
        padding: 0.4rem 0.8rem;

        .user-avatar {
          width: 30px;
          height: 30px;
        }

        .user-name {
          font-size: 0.85rem;
        }
      }

      .user-dropdown {
        min-width: 200px;
        right: -15px;
        top: calc(100% + 10px) !important;
        position: absolute !important;
        z-index: 1050 !important;
        background: white !important;
        border: 1px solid rgba(0, 0, 0, 0.15) !important;
        border-radius: 8px !important;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;

        .dropdown-item {
          padding: 8px 12px;

          span {
            font-size: 0.8rem;
          }

          i,
          .menu-icon {
            width: 16px;
          }

          .menu-icon {
            svg {
              width: 14px;
              height: 14px;
            }
          }
        }
      }
    }
  }

  // Fix user dropdown positioning on tablet and mobile
  .navbar-nav {
    position: static !important;

    .user-dropdown {
      position: fixed !important;
      top: 60px !important;
      // right: 10px !important;
      left: auto !important;
      z-index: 1050 !important;
      min-width: 220px !important;
      max-width: 280px !important;
      max-height: 400px !important;
      overflow-y: auto !important;
      background: white !important;
      border: 1px solid rgba(0, 0, 0, 0.15) !important;
      border-radius: 8px !important;
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15) !important;

      .dropdown-item {
        padding: 12px 15px !important;
        font-size: 0.9rem !important;

        .menu-icon {
          width: 20px !important;
          height: 20px !important;
        }
      }
    }

    // User link responsive for tablet
    .user-link {
      padding: 0.35rem 0.7rem !important;
      font-size: 0.8rem !important;
      min-width: 90px !important;
      min-height: 32px !important;
      box-sizing: border-box !important;
    }
  }
}

// Responsive for Load More Button - 425px
@media (max-width: 425px) {
  .btn-primary.btn-lg.px-5 {
    padding: 0.8rem 1.5rem !important;
    font-size: 0.9rem !important;
    white-space: nowrap !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    min-width: auto !important;
    width: auto !important;

    i {
      font-size: 0.8rem !important;
      margin-left: 0.5rem !important;
    }

    span {
      line-height: 1.2 !important;
    }
  }
}

@media (max-width: 480px) {
  .all-properties-page {
    padding-top: 60px; // Further reduced padding for small mobile
  }

  .btn-primary.btn-lg.px-5 {
    padding: 0.7rem 1.2rem !important;
    font-size: 0.85rem !important;
    white-space: nowrap !important;
   width: 47%;


    i {
      font-size: 0.75rem !important;
    }
  }

  .home-header {
    .navbar {
      .navbar-brand {
        .logo-img {
          height: 40px;
        }
      }
    }

    // Fix user dropdown positioning on small mobile
    .navbar-nav {
      .user-dropdown {
        position: fixed !important;
        top: 50px !important;
        // right: 5px !important;
        left: auto !important;
        min-width: 200px !important;
        max-width: 250px !important;
        max-height: 350px !important;

        .dropdown-item {
          padding: 10px 12px !important;
          font-size: 0.85rem !important;

          .menu-icon {
            width: 18px !important;
            height: 18px !important;
          }
        }
      }
    }

    // User link responsive for small mobile
    .user-link {
      padding: 0.2rem 0.4rem !important;
      font-size: 0.65rem !important;
      min-width: 60px !important;
      min-height: 28px !important;
      box-sizing: border-box !important;

      span {
        display: none !important;
      }

      i {
        margin-right: 0 !important;
      }
    }
  }
}

// Extra small screens for Load More Button
@media (max-width: 375px) {
  .btn-primary.btn-lg.px-5 {
    padding: 0.6rem 1rem !important;
    font-size: 0.8rem !important;

    i {
      font-size: 0.7rem !important;
      margin-left: 0.4rem !important;
    }
  }
}

// Mobile Navigation Styles - Copied from Home Page
.navbar-toggler {
  border: none;
  background: transparent;
  padding: 8px 12px;
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(0, 123, 255, 0.1);
  }

  &:focus {
    box-shadow: none;
    outline: none;
  }

  .navbar-toggler-icon {
    display: flex;
    align-items: center;
    justify-content: center;

    i {
      font-size: 1.2rem;
      color: #333;
      transition: all 0.3s ease;
    }
  }
}

// Mobile Navigation Dropdown - Copied from Home Page
.mobile-nav-dropdown {
  position: absolute;
  top: calc(100% + -1.8rem) !important;
  left: 50%;
  transform: translateX(-50%);
  margin-left: -25px !important;
  width: 200px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  z-index: 1040;
  border: 1px solid rgba(0, 0, 0, 0.08);
  overflow: visible;
  animation: dropdownFadeIn 0.3s ease;
  margin-top: 0;
  font-family: "Noto Kufi Arabic" !important;

  .dropdown-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    color: #333;
    text-decoration: none;
    transition: all 0.2s ease;
    cursor: pointer;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);

    &:last-child {
      border-bottom: none;
    }

    &:hover {
      background: rgba(0, 123, 255, 0.05);
      color: #007bff;
    }

    .menu-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 18px;
      height: 18px;

      i {
        font-size: 0.8rem;
      }
    }
  }

  .dropdown-divider {
    margin: 4px 0;
    border-top: 1px solid rgba(0, 0, 0, 0.08);
  }

  .mobile-language-toggle {
    padding: 8px 12px;
    border-top: 1px solid rgba(0, 0, 0, 0.08);
  }
}

// Dropdown animation
@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

// Force horizontal layout on all mobile screens
@media (max-width: 991.98px) {
  .all-properties-page {
    overflow-x: hidden !important;
  }

  .home-header .navbar {
    .container-fluid {
      display: flex !important;
      flex-direction: row !important;
      justify-content: space-between !important;
      align-items: center !important;
      flex-wrap: nowrap !important;
      overflow: hidden !important;
      padding: 0 0.5rem;
    }

    // Ensure all direct children stay on same line
    > .container-fluid > * {
      flex-shrink: 1 !important;
      white-space: nowrap !important;
    }

    .nav-list {
      gap: 1rem !important;
    }
  }
}

// Very small screens - Hide username only below 320px
@media (max-width: 319px) {
  .navbar {
    .navbar-nav {
      .user-profile {
        .user-name {
          display: none !important; // Hide username only on very small screens
        }
      }
    }
  }
}

/* إصلاح انعكاس الأيقونات في Mobile Navigation Dropdown للعربية */
:host-context(html[dir="rtl"]) .mobile-nav-dropdown,
:host-context(html[lang="ar"]) .mobile-nav-dropdown {
  .dropdown-item {
    direction: rtl !important;
    text-align: right !important;
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    flex-direction: row !important;
    padding: 0.75rem 1rem !important;

    .menu-icon {
      margin-left: 0 !important;
      margin-right: auto !important;
      order: 999 !important; /* أقصى اليمين */
      flex-shrink: 0 !important;

      i {
        transform: none !important; /* منع انعكاس الأيقونات */
        direction: ltr !important;
        font-size: 1.1rem !important;
      }
    }

    span:not(.menu-icon) {
      order: 1 !important;
      font-family: "Noto Kufi Arabic" !important;
      letter-spacing: 0 !important;
      word-spacing: 0 !important;
      flex-grow: 1 !important;
      text-align: right !important;
    }

    a {
      order: 1 !important;
      font-family: "Noto Kufi Arabic" !important;
      letter-spacing: 0 !important;
      word-spacing: 0 !important;
      text-decoration: none !important;
      flex-grow: 1 !important;
      text-align: right !important;
    }
  }

  /* إصلاح الـ language toggle في الموبايل */
  .mobile-language-toggle {
    justify-content: center !important;
    flex-direction: row !important;
  }
}

/* منع انعكاس جميع الأيقونات في العربية */
:host-context(html[dir="rtl"]) i,
:host-context(html[lang="ar"]) i {
  transform: none !important;
  direction: ltr !important;
}

:host-context(html[dir="rtl"]) .fas,
:host-context(html[lang="ar"]) .fas,
:host-context(html[dir="rtl"]) .far,
:host-context(html[lang="ar"]) .far,
:host-context(html[dir="rtl"]) .fab,
:host-context(html[lang="ar"]) .fab {
  transform: none !important;
  direction: ltr !important;
}

/* إصلاح خاص للأيقونات في الموبايل */
@media (max-width: 991.98px) {
  :host-context(html[dir="rtl"]) .mobile-nav-dropdown,
  :host-context(html[lang="ar"]) .mobile-nav-dropdown {
    .dropdown-item {
      display: flex !important;
      justify-content: space-between !important;
      align-items: center !important;
      flex-direction: row !important;
      padding: 0.75rem 1rem !important;
    }

    .menu-icon {
      position: absolute !important;
      right: 1rem !important;
      left: auto !important;
      margin: 0 !important;

      i {
        transform: none !important;
        direction: ltr !important;
        display: inline-block !important;
        font-size: 1.1rem !important;
      }
    }

    span:not(.menu-icon),
    a {
      padding-right: 3rem !important; /* مساحة للأيقونة */
      padding-left: 0 !important;
      width: 100% !important;
      text-align: right !important;
    }
  }

  :host-context(html[dir="rtl"]) .menu-icon i,
  :host-context(html[lang="ar"]) .menu-icon i {
    transform: none !important;
    direction: ltr !important;
  }
}

// Responsive Design for Properties
@media (max-width: 768px) {
  .properties-section {
    padding: 60px 0;

    .section-title {
      font-size: 2rem;
      margin-bottom: 2rem;
    }
  }

  .property-card {
    margin-bottom: 20px;

    .property-image {
      height: 180px;
    }

    .property-content {
      padding: 15px;

      .property-title {
        font-size: 1.2rem;
      }

      .property-price .price {
        font-size: 1.2rem;
      }
    }
  }

  .home-header {
    .navbar {
      padding: 0.75rem 0;

      .navbar-brand img {
        height: 45px;
      }
    }

    .hero-section {
      height: 150px;
    }

    .user-dropdown {
      min-width: 200px;
      right: -1rem;
    }
  }
}

@media (max-width: 576px) {
  .properties-section {
    padding: 40px 0;

    .section-title {
      font-size: 1.8rem;
      margin-bottom: 1.5rem;
    }
  }

  .property-card {
    .property-image {
      height: 160px;

      .property-badge {
        font-size: 0.7rem;
        padding: 4px 8px;
      }

      .property-location {
        font-size: 0.7rem;
        padding: 4px 8px;
      }
    }

    .property-content {
      padding: 12px;

      .property-title {
        font-size: 1.1rem;
      }

      .property-description {
        font-size: 0.8rem;
      }

      .property-price .price {
        font-size: 1.1rem;
      }

      .property-rating {
        .stars i {
          font-size: 0.99rem;
        }

        .rating-text {
          font-size: 1.1rem;
        }
      }

      .property-actions {
        .btn {
          padding: 6px 10px;
          font-size: 0.8rem;
        }
      }
    }
  }
}

/* Property Card Responsive Dimensions */
@media (min-width: 1200px) {
  .property-card {
    width: 100% !important;
    max-width: 320px !important;
    height: 230px !important;
  }

  .properties-section .row.g-4 {
    gap: 5px !important;
    justify-content: space-between !important;

    .col-lg-3 {
      width: calc(25% - 15px) !important;
      max-width: 320px !important;
    }
  }
}

@media (max-width: 1199px) and (min-width: 992px) {
  .property-card {
    width: 100% !important;
    max-width: 250px !important;
    height: 260px !important;

    .property-content {
      .property-bottom-row {
        .property-rating {
          flex-wrap: nowrap !important;
          min-width: 0 !important;

          .stars {
            gap: 0px !important;
            flex-shrink: 0 !important;

            i {
              font-size: 0.75rem !important;
            }
          }

          .rating-text {
            font-size: 0.85rem !important;
            margin-left: 2px !important;
            flex-shrink: 0 !important;
          }

          .reviews-count {
            font-size: 0.8rem !important;
            margin-left: 2px !important;
            white-space: nowrap !important;
            flex-shrink: 1 !important;
            overflow: hidden !important;
            text-overflow: ellipsis !important;
          }
        }
      }
    }
  }

  .properties-section .row.g-4 {
    gap: 10px !important;

    .col-lg-3 {
      width: calc(25% - 8px) !important;
      max-width: 250px !important;
    }
  }
}

@media (max-width: 991px) and (min-width: 768px) {
  .property-card {
    width: 100% !important;
    max-width: 220px !important;
    height: 250px !important;
  }

  .properties-section .row.g-4 {
    gap: 8px !important;

    .col-lg-3 {
      width: calc(50% - 4px) !important;
      max-width: 220px !important;
    }
  }
}

@media (max-width: 767px) {
  .property-card {
    width: 100% !important;
    max-width: 280px !important;
    height: 230px !important;
    margin: 0 auto !important;
  }

  .properties-section .row.g-4 {
    gap: 15px !important;

    .col-lg-3 {
      width: 100% !important;
      max-width: 220px !important;
    }
  }
}

/* Property Image Responsive Dimensions */
@media (min-width: 1200px) {
  .property-image {
    width: 100% !important;
    height: 320px !important;
  }
}

@media (max-width: 1199px) and (min-width: 992px) {
  .property-image {
    width: 100% !important;
    height: 280px !important;
  }
}

@media (max-width: 991px) and (min-width: 768px) {
  .property-image {
    width: 100% !important;
    height: 250px !important;
  }
}

@media (max-width: 767px) {
  .property-image {
    width: 100% !important;
    height: 220px !important;
  }
}

// Font Imports
@import url("https://fonts.googleapis.com/css2?family=Noto+Kufi+Arabic:wght@100;200;300;400;500;600;700;800;900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Markazi+Text:wght@400;500;600;700&display=swap");

// Properties Section
.properties-section {
  padding: 20px 0;
  font-family: "Markazi Text";

  // Properties Row with Gap
  .row.g-4 {
    gap: 5px;
    justify-content: space-between;

    .col-lg-3 {
      flex: 0 0 auto;
      width: calc(25% - 15px);
      max-width: 320px;
    }
  }

  .section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 2rem;
    position: relative;
    font-family: "Markazi Text";

    &::after {
      content: "";
      position: absolute;
      bottom: -15px;
      left: 50%;
      transform: translateX(-50%);
      width: 80px;
      height: 4px;
      border-radius: 2px;
    }
  }
}

.property-card {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  height: 400px;
  width: 100%;
  max-width: 320px;
  opacity: 1;
  margin: 0 auto;
  cursor: pointer;
  border: 1px solid #f0f0f0;
  font-family: "Markazi Text";
  display: flex;
  flex-direction: column;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  }

  .property-image {
    position: relative;
    width: 100%;
    height: 320px;
    opacity: 1;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    overflow: hidden;
    transform: rotate(0deg);

    &::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 50%;
      background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
      z-index: 1;
      pointer-events: none;
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.3s ease;
    }

    &:hover img {
      transform: scale(1.05);
    }

    // Top Badges Container
    .property-top-badges {
      position: absolute;
      top: 12px;
      right: 12px;
      display: flex;
      align-items: flex-start;
      width: auto;
      height: 24px;
      gap: 4px;
      z-index: 2;
    }

    // English layout - left positioning
    :host-context(html[dir="ltr"]) .property-top-badges,
    :host-context(html[lang="en"]) .property-top-badges {
      left: 12px;
    }

    // English layout - property location left positioning
    :host-context(html[dir="ltr"]) .property-location-text,
    :host-context(html[lang="en"]) .property-location-text {
      left: 12px;
    }

    // English layout - smaller font for Featured badge
    :host-context(html[dir="ltr"]) .property-badge-featured,
    :host-context(html[lang="en"]) .property-badge-featured {
      font-size: 0.7rem !important;
    }

    // Featured Badge
    .property-badge-featured {
      background: #ffffff;
      color: #333333;
      padding: 4px 10px;
      border-radius: 24px;
      font-size: 1.1rem;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      height: 24px;
      display: flex;
      align-items: center;
      gap: 4px;
      white-space: nowrap;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      font-family: "Noto Kufi Arabic";

      i {
        color: #ffc107;
        font-size: 0.8rem;
      }
    }

    // Price Badge
    .property-price-overlay {
      background: #ffffff;
      color: #007bff;
      padding: 4px 10px;
      border-radius: 24px;
      font-size: 1.15rem;
      font-weight: 400;
      height: 24px;
      display: flex;
      align-items: center;
      gap: 4px;
      white-space: nowrap;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      font-family: "Markazi Text";

      i {
        color: #28a745;
        font-size: 0.8rem;
      }

      .price-amount {
        color: #007bff;
      }
    }

    // Property Type - Bottom Left
    .property-type-badge {
      position: absolute;
      bottom: 77px;
      left: 12px;
      background: #ff6b35;
      color: white;
      padding: 1px 12px;
      border-radius: 4px;
      font-size: 0.99rem;
      font-weight: 600;
      text-transform: capitalize;
      z-index: 2;
      font-family: "Markazi Text";
    }

    // Property Title - Over Image
    .property-title {
      position: absolute;
      bottom: 45px;
      left: 12px;
      right: 12px;
      font-size: 0.95rem;
      font-weight: 600;
      color: white;
      margin-bottom: 0;
      line-height: 1.3;
      display: flex;
      align-items: center;
      gap: 6px;
      font-family: "Noto Kufi Arabic", "Markazi Text", sans-serif;
      z-index: 2;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.7);
      flex-wrap: wrap;

      .verified-icon {
        color: #28a745;
        font-size: 0.8rem;
        margin-left: 4px;
      }
    }

    // Property Location - Over Image
    .property-location-text {
      position: absolute;
      bottom: 20px;
      right: 12px;
      color: rgba(255, 255, 255, 0.9);
      font-size: 0.85rem;
      margin-bottom: 0;
      display: flex;
      align-items: center;
      gap: 6px;
      font-family: "Noto Kufi Arabic", "Markazi Text", sans-serif;
      z-index: 2;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.7);

      i {
        color: rgba(255, 255, 255, 0.9);
        font-size: 0.8rem;
      }
    }
  }

  .property-content {
    padding: 5px;

    // Bottom Row with Rating and Actions (New Design)
    .property-bottom-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-left: 10px;

      .property-rating {
        display: flex;
        align-items: center;

        .stars {
          display: flex;
          gap: 1px;

          i {
            color: #ffc107;
            font-size: 0.8rem;
          }
        }

        .rating-text {
          font-weight: 600;
          color: #2c3e50;
          font-size: 1.1rem;
          margin-left: 4px;
          font-family: "Markazi Text";
        }

        .reviews-count {
          color: #6c757d;
          font-size: 1.0rem;
          font-family: "Markazi Text";
        }
      }

      .property-actions {
        display: flex;
        gap: 6px;
        flex: 0 0 auto;

        .btn-icon {
          background: none;
          border: none;
          color: #6c757d;
          padding: 6px;
          border-radius: 50%;
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;

          i {
            font-size: 0.85rem;
          }
        }
      }
    }
  }
}

/* Clean Header Styles */
.simple-header {
  padding: 1.25rem 1.5rem;
  margin: 1rem -15px 1.5rem -15px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 3px solid #28a745;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border-radius: 12px;

  h1 {
    font-weight: 600;
    color: #28a745;
    margin-bottom: 0.25rem;
    font-size: 1.75rem;
  }

  p {
    color: #6c757d;
    margin-bottom: 0;
    font-size: 0.95rem;
  }

  .btn {
    border-radius: 8px;
    padding: 0.5rem 1.25rem;
    font-weight: 500;
    transition: all 0.2s ease;
    background: #28a745;
    border: none;
    color: white;
    font-size: 0.9rem;

    &:hover {
      background: #218838;
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
    }
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .simple-header {
    padding: 1rem 1.25rem;

    .d-flex {
      flex-direction: column;
      align-items: flex-start !important;
      gap: 0.75rem;
    }

    h1 {
      font-size: 1.5rem;
    }

    p {
      font-size: 0.9rem;
    }

    .btn {
      align-self: flex-start;
      padding: 0.45rem 1rem;
      font-size: 0.85rem;
    }
  }
}

/* Green Theme Colors */
.property-card {
  .property-badge .badge {
    background-color: #28a745 !important;
    color: white;
  }

  .property-price h5 {
    color: #28a745 !important;
  }

  .btn-outline-primary {
    border-color: #28a745;
    color: #28a745;

    &:hover {
      background-color: #28a745;
      border-color: #28a745;
      color: white;
    }

    &:focus {
      box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }
  }
}

/* Custom Alert Success Styling */
.alert.alert-success {
  background: linear-gradient(135deg, #e8f5e8, #c8e6c9) !important;
  color: #2e7d32 !important;
  width: 21% !important;
  margin: 0 auto !important;
  text-align: center !important;
  border: 1px solid #4caf50 !important;
  border-radius: 8px !important;
  font-weight: 500 !important;
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.2) !important;

  // Responsive design for mobile
  @media (max-width: 768px) {
    width: 60% !important;
  }

  @media (max-width: 480px) {
    width: 80% !important;
  }
}

/* Green Spinner */
.spinner-border.text-primary {
  color: #28a745 !important;
}

/* Green Load More Button */
.btn-primary {
  background-color: #28a745;
  border-color: #28a745;

  &:hover {
    background-color: #218838;
    border-color: #1e7e34;
  }

  &:focus {
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
  }
}

// ===== NAVBAR DROPDOWN STYLING =====

.home-header {
  .navbar {
    .user-dropdown {
      .dropdown-item {
        span {
          font-size: 1.2rem !important;
        }
      }
    }
  }
}

// ===== LOCATION CARDS ROW STYLING =====

.row.justify-content-center.g-2.g-md-3.mt-5 {
  padding-bottom: 3% !important;
}

// ===== USER ICON WHITE COLOR =====

.fa-user {
  color: white !important;
}

// ===== UNIFIED SECTION PADDING (EXCLUDING all-properties-page) =====

// Main sections padding (excluding the main page wrapper)
.properties-section,
.newsletter-section,
.download-app-footer {
  padding: 4rem 0 !important;
}

// Filter and content areas padding
.container-fluid.py-5 {
  padding: 4rem 1.5rem !important;
}

// Container padding for sections
.properties-section .container,
.newsletter-section .container,
.download-app-footer .container {
  padding-left: 1.5rem !important;
  padding-right: 1.5rem !important;
}

// Responsive padding for tablets
@media (max-width: 768px) {
  .properties-section,
  .newsletter-section,
  .download-app-footer {
    padding: 3rem 0 !important;
  }

  .container-fluid.py-5 {
    padding: 3rem 1rem !important;
  }

  .properties-section .container,
  .newsletter-section .container,
  .download-app-footer .container {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }
}

// Responsive padding for mobile
@media (max-width: 576px) {
  .properties-section,
  .newsletter-section,
  .download-app-footer {
    padding: 2rem 0 !important;
  }

  .container-fluid.py-5 {
    padding: 2rem 0.75rem !important;
  }

  .properties-section .container,
  .newsletter-section .container,
  .download-app-footer .container {
    padding-left: 0.75rem !important;
    padding-right: 0.75rem !important;
  }
}

/* إصلاح Property Cards في العربية */
:host-context(html[dir="rtl"]) .property-badge,
:host-context(html[lang="ar"]) .property-badge,
:host-context(.rtl) .property-badge {
  right: 1rem !important;
  left: auto !important;
}

:host-context(html[dir="rtl"]) .property-location,
:host-context(html[lang="ar"]) .property-location,
:host-context(.rtl) .property-location {
  right: 1rem !important;
  left: auto !important;
  text-align: right !important;
  flex-direction: row-reverse !important;
}

:host-context(html[dir="rtl"]) .property-location i,
:host-context(html[lang="ar"]) .property-location i,
:host-context(.rtl) .property-location i {
  margin-left: 0.5rem !important;
  margin-right: 0 !important;
}

:host-context(html[dir="rtl"]) .card-body,
:host-context(html[lang="ar"]) .card-body,
:host-context(.rtl) .card-body {
  text-align: right !important;
  direction: rtl !important;
}

:host-context(html[dir="rtl"]) .property-price,
:host-context(html[lang="ar"]) .property-price,
:host-context(.rtl) .property-price {
  text-align: right !important;
}

/* إصلاح Header في العربية */
:host-context(html[dir="rtl"]) .simple-header,
:host-context(html[lang="ar"]) .simple-header,
:host-context(.rtl) .simple-header {
  .d-flex {
    direction: rtl;
  }

  h1 {
    text-align: right;
  }

  p {
    text-align: right;
  }
}

// Base container styles
.container-fluid {
  transition: padding 0.3s ease;
}

.property-card {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  overflow: hidden;
  border: 1px solid #f0f0f0;

  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    border-color: #007bff;
  }

  .property-image {
    position: relative;
    overflow: hidden;

    img {
      transition: transform 0.3s ease;
    }

    &:hover img {
      transform: scale(1.05);
    }
  }

  .property-badge {
    .badge {
      border-radius: 20px;
      font-weight: 600;
      letter-spacing: 0.5px;
    }
  }

  .property-location {
    div {
      border-radius: 20px;
      font-size: 0.875rem;
      font-weight: 500;
    }
  }

  .property-content {
    .property-title {
      color: #2c3e50;
      font-size: 1.25rem;
      line-height: 1.4;
    }

    .property-details {
      small {
        font-size: 0.75rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      span {
        font-size: 0.9rem;
        color: #2c3e50;
      }
    }

    .property-price {
      h5 {
        font-size: 1.4rem;
        font-weight: 700;
      }
    }

    .btn {
      border-radius: 8px;
      font-weight: 600;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
      }
    }
  }
}

// Header Styles
.display-4 {
  background: linear-gradient(135deg, #007bff, #0056b3);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

// Search Styles
.search-container {
  .input-group {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    &:focus-within {
      box-shadow: 0 6px 20px rgba(0, 123, 255, 0.2);
      transform: translateY(-2px);
    }

    .input-group-text {
      border: 1px solid #e0e0e0;
      border-right: none;
      border-radius: 8px 0 0 8px;
      padding: 0.75rem 1rem;

      i {
        font-size: 1.1rem;
      }
    }

    .form-control {
      border: 1px solid #e0e0e0;
      border-left: none;
      padding: 0.75rem 1rem;
      font-size: 1rem;

      &:focus {
        border-color: #007bff;
        box-shadow: none;
      }

      &::placeholder {
        color: #999;
        font-style: italic;
      }
    }
  }
}

// Alert Styles
.alert {
  border-radius: 12px;
  border: none;
  font-weight: 500;

  &.alert-info {
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    color: #1565c0;
  }

  &.alert-success {
    background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
    color: #2e7d32;
  }
}

// Button Styles
.btn {
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;

  &.btn-primary {
    background: #232176;
    border: none;

    &:hover {
      background: linear-gradient(135deg, #0056b3, #004085);
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
    }
  }

  &.btn-outline-primary {
    border: 2px solid #007bff;
    color: #007bff;
    background: transparent;

    &:hover {
      background: #007bff;
      color: white;
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0, 123, 255, 0.3);
    }
  }
}

// Loading Spinner
.spinner-border {
  animation: spinner-border 0.75s linear infinite;
}

// Empty State
.empty-state {
  padding: 3rem 1rem;

  i {
    opacity: 0.5;
  }

  h3 {
    margin-top: 1.5rem;
    font-weight: 600;
  }

  p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
  }
}

// Grid spacing adjustments
.row {
  &.g-3 {
    --bs-gutter-x: 1rem;
    --bs-gutter-y: 1rem;
  }

  &.g-md-4 {
    @media (min-width: 768px) {
      --bs-gutter-x: 1.5rem;
      --bs-gutter-y: 1.5rem;
    }
  }
}

// Responsive Design
// Extra Large screens (1200px and up)
@media (min-width: 1200px) {
  .container-fluid {
    max-width: 1400px;
    margin: 0 auto;
  }

  .search-container {
    max-width: 600px !important;
  }
}

// Large screens (992px and up)
@media (min-width: 992px) and (max-width: 1199px) {
  .search-container {
    max-width: 550px !important;
  }

  .property-card {
    .property-content {
      padding: 1.25rem !important;
    }
  }
}

// Medium screens (768px and up)
@media (min-width: 768px) and (max-width: 991px) {
  .search-container {
    max-width: 500px !important;
  }

  .property-card {
    margin-bottom: 1.5rem;

    .property-content {
      // padding: 1rem !important;
    }
  }

  .display-4 {
    font-size: 2.5rem;
  }
}

// Small screens (576px and up)
@media (min-width: 576px) and (max-width: 767px) {
  .property-card {
    margin-bottom: 1.5rem;
  }

  .display-4 {
    font-size: 2rem;
  }

  .property-content {
    // padding: 1rem !important;

    .property-title {
      font-size: 1.1rem;
    }

    .property-price h5 {
      font-size: 1.2rem;
    }
  }

  .btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
  }

  .search-container {
    max-width: 100% !important;

    .input-group {
      .input-group-text {
        padding: 0.6rem 0.8rem;
      }

      .form-control {
        padding: 0.6rem 0.8rem;
        font-size: 0.9rem;
      }
    }
  }
}

// Extra Small screens (less than 576px)
@media (max-width: 575px) {
  .container-fluid {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }

  .display-4 {
    font-size: 1.75rem;
    text-align: center;
  }

  .text-muted {
    text-align: center;
    font-size: 1rem;
  }

  .btn-lg {
    width: 100%;
    margin-top: 1rem;
    padding: 0.75rem;
    font-size: 0.9rem;
  }

  .search-container {
    max-width: 100% !important;
    padding: 0 0.5rem;

    .input-group {
      .input-group-text {
        padding: 0.5rem 0.75rem;

        i {
          font-size: 1rem;
        }
      }

      .form-control {
        padding: 0.5rem 0.75rem;
        font-size: 0.85rem;

        &::placeholder {
          font-size: 0.8rem;
        }
      }
    }
  }

  .property-card {
    margin-bottom: 1.25rem;

    .property-image img {
      height: 180px !important;
    }

    .property-content {
      // padding: 0.75rem !important;

      .property-title {
        font-size: 1rem;
        margin-bottom: 0.75rem;
      }

      .property-details {
        margin-bottom: 0.75rem;

        .row {
          gap: 0.25rem;
        }

        .col-6 {
          margin-bottom: 0.5rem;
        }

        small {
          font-size: 0.7rem;
        }

        span {
          font-size: 0.8rem;
        }
      }

      .property-price h5 {
        font-size: 1.1rem;
        margin-bottom: 0.75rem;
      }

      .btn {
        padding: 0.5rem;
        font-size: 0.85rem;
      }
    }
  }

  .empty-state {
    padding: 2rem 0.5rem;

    i {
      font-size: 3rem !important;
    }

    h3 {
      font-size: 1.25rem;
    }

    p {
      font-size: 0.9rem;
    }
  }

  // Header responsive adjustments
  .d-flex.justify-content-between {
    flex-direction: column;
    gap: 1rem;

    .btn-outline-primary {
      align-self: center;
    }
  }
}

/* No Properties Found State */
.no-properties-found {
  padding: 3rem 1rem;

  i {
    opacity: 0.6;
  }

  h4 {
    margin-bottom: 1rem;
    font-family: "Noto Kufi Arabic", "Markazi Text", sans-serif;
  }

  p {
    font-family: "Markazi Text", sans-serif;
    font-size: 1.1rem;
  }
}

/* RTL Support for No Properties State */
:host-context(html[dir="rtl"]) .no-properties-found,
:host-context(html[lang="ar"]) .no-properties-found {
  h4,
  p {
    font-family: "Noto Kufi Arabic", sans-serif;
    direction: rtl;
    text-align: center;
  }
}

// Newsletter Section
.newsletter-section {
  background: #f5f5f5;
  .newsletter-card {
    // background: white;
    // border-radius: 20px;
    // padding: 40px;
    // box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    // border: 1px solid #e9ecef;

    .newsletter-content {
      .newsletter-text-section {
        display: flex;
        align-items: center;
        gap: 20px;
        white-space: nowrap;

        .email-icon-container {
          flex-shrink: 0;

          .email-icon {
            width: 70px;
            height: 70px;
            margin-top: 20px;
            object-fit: contain;
          }
        }

        .newsletter-title {
          font-size: 1.4rem;
          font-weight: 700;
          color: #000c66;
          margin: 0;
          font-family: "Noto Kufi Arabic";
          white-space: nowrap;
          flex-shrink: 0;

          .green-text {
            color: #00ec42 !important;
          }
        }

        .newsletter-description {
          font-size: 0.9rem;
          color: #666;
          margin: 0;
          font-family: "Noto Kufi Arabic";
          white-space: nowrap;
          flex-shrink: 0;
        }

        .newsletter-input {
          flex: 2;
          min-width: 400px;
          max-width: 400px;
          padding: 15px 20px;
          border: 2px solid #e9ecef;
          border-top-left-radius: 0px;
          border-bottom-left-radius: 0px;
          font-size: 1rem;
          height: 56px;

          &:focus {
            border-color: #000c66;
            box-shadow: 0 0 0 0.2rem rgba(0, 12, 102, 0.25);
          }

          &::placeholder {
            font-size: 0.9rem;
            color: #6c757d;
            overflow: visible;
          }
        }

        .newsletter-btn {
          background-color: #000c66;
          color: white;
          border: none;
          padding: 12px 24px;
          border-top-right-radius: 0px;
          border-bottom-right-radius: 0px;
          margin-right: -2%;
          font-weight: 600;
          height: 55px;
          font-size: 1rem;
          white-space: nowrap;
          flex-shrink: 0;
          transition: all 0.3s ease;

          &:hover {
            background-color: #00660f;
            transform: translateY(-2px);
          }
        }
      }
    }
  }
}


// Special responsive design for screens 320px to 425px
@media (min-width: 320px) and (max-width: 425px) {
  .newsletter-section {
    .newsletter-card {
      .newsletter-content {
        .newsletter-text-section {
          .newsletter-title {
            .green-text {
              display: flex !important;
              justify-content: center !important;
              align-items: center !important;
              text-align: center !important;
              width: 100% !important;
              margin: 0 auto !important;
              font-size: 1.1rem !important;
              font-weight: 700 !important;
            }
          }
        }
      }
    }
  }
}

// Download App Footer Section - Updated
.download-app-footer {
  // background-color: #f8f9fa;
  // border-top: 1px solid #e9ecef;

  .row {
    align-items: center;
  }

  // Logo Section
  .logo-section {
    .footer-logo {
      height: 60px;
      width: auto;
      margin-bottom: 15px;
    }

    .contact-info {
      .contact-text {
        font-size: 1rem;
        font-weight: bold;
        color: #031752;
        margin-bottom: 8px;
        line-height: 1.4;
        font-family: "Noto Kufi Arabic";
      }

      .contact-details {
        font-size: 0.9rem;
        color: #666;
        margin-bottom: 5px;
        line-height: 1.3;
        font-family: "Noto Kufi Arabic";
      }

      .contact-email {
        font-size: 0.9rem;
        color: #000c66;
        margin-bottom: 15px;
      }

      .social-icons {
        display: flex;
        gap: 10px;
        margin-right: 20%;
      }

      .social-icon {
        display: flex !important;
        width: 35px !important;
        height: 35px !important;
        background-color: #000c66 !important;
        color: white !important;
        border-radius: 50% !important;
        align-items: center !important;
        justify-content: center !important;
        font-size: 16px !important;
        cursor: pointer !important;
        transition: all 0.3s ease;

        &:hover {
          background-color: #000a55 !important;
          transform: scale(1.1);
        }
      }
    }
  }

  // Download Text Section
  .download-text-section {
    .download-title {
      font-size: 2.5rem;
      font-weight: bold;
      color: #28a745;
      margin: 0;
      font-family: "Noto Kufi Arabic";
      text-align: center;
    }
  }

  // App Buttons Section
  .app-buttons-section {
    .app-buttons {
      display: flex;
      gap: 15px;
      align-items: center;
      justify-content: center;

      .app-store-btn,
      .google-play-btn {
        height: 60px;
        width: auto;
        transition: all 0.3s ease;
        cursor: pointer;

        &:hover {
          transform: scale(1.05);
          box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
      }
    }
  }

  // Bottom Section
  .bottom-section {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;

    .links-section {
      .footer-links {
        display: flex;
        gap: 30px;
        align-items: center;
        justify-content: center;
        flex-wrap: wrap;

        .footer-link {
          font-size: 1rem;
          font-family: "Noto Kufi Arabic";
          text-decoration: none;
          color: #666;
          transition: all 0.3s ease;

          &:hover {
            color: #000c66;
          }

          &.footer-link-bold {
            color: #000c66;
            font-weight: 600;
          }
        }
      }
    }

    .join-section {
      margin-top: 20px;

      .join-community {
        display: flex;
        gap: 15px;
        align-items: center;
        justify-content: center;
        flex-wrap: wrap;

        .join-text {
          font-size: 1rem;
          color: #666;
          font-family: "Noto Kufi Arabic";
        }

        .social-link {
          display: inline-flex;
          align-items: center;
          justify-content: center;
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background-color: #f8f9fa;
          color: #666;
          text-decoration: none;
          transition: all 0.3s ease;

          &:hover {
            background-color: #000c66;
            color: white;
            transform: translateY(-2px);
          }

          i {
            font-size: 1.2rem;
          }
        }
      }
    }
  }
}

// RTL Support for Newsletter
:host-context(html[dir="rtl"]) .newsletter-section,
:host-context(html[lang="ar"]) .newsletter-section {
  .newsletter-card {
    .newsletter-content {
      .newsletter-text-section {
        .newsletter-title,
        .newsletter-description {
          text-align: right;
        }

        .newsletter-input {
          // margin-right: 0px;
        }
      }
    }
  }
}

// LTR Support for Newsletter
:host-context(html[dir="ltr"]) .newsletter-section,
:host-context(html[lang="en"]) .newsletter-section {
  .newsletter-card {
    .newsletter-content {
      .newsletter-text-section {
        .newsletter-title,
        .newsletter-description {
          text-align: left;
          font-family: "Markazi text", sans-serif;
        }

        .newsletter-title {
          .green-text {
            color: #00ec42 !important;
          }
        }

        .newsletter-input {
          margin-left: 100px;
        }
      }
    }
  }
}

// RTL Support for Download App Footer
:host-context(html[dir="rtl"]) .download-app-footer,
:host-context(html[lang="ar"]) .download-app-footer {
  .app-buttons-section {
    .app-buttons {
      align-items: flex-end;
      justify-content: flex-end;
    }

    .footer-links-container {
      margin: 0 350px !important;
    }
  }

  .download-text-section {
    text-align: center;
  }

  .logo-contact-section {
    .logo-section {
      text-align: right;

      .contact-info {
        .social-icons {
          // justify-content: flex-end;
        }
      }
    }
  }
}

// LTR Support for Download App Footer (English)
:host-context(html[dir="ltr"]) .download-app-footer,
:host-context(html[lang="en"]) .download-app-footer {
  // Fix for 1024px screens in English
  @media (min-width: 1024px) and (max-width: 1199px) {
    .row.align-items-center.d-lg-flex.d-xl-none {
      flex-direction: row !important;
      text-align: left !important;

      .col-lg-3:first-child {
        order: 1 !important;
      }

      .col-lg-6 {
        order: 2 !important;
        text-align: center !important;
      }

      .col-lg-3:last-child {
        order: 3 !important;
      }
    }

    .logo-section {
      text-align: left !important;
    }

    .app-buttons {
      justify-content: flex-end !important;
    }

    .social-icons {
      justify-content: flex-start !important;
      margin-right: 0 !important;
      margin-left: 0 !important;
    }
  }

  .app-buttons-section {
    .app-buttons {
      align-items: flex-end;
      justify-content: flex-end;
      margin-right: 10%;
    }

    .footer-links-container {
      margin: 0 180px !important;
    }
  }

  .download-text-section {
    text-align: center;

    .download-title {
      font-family: "inter" !important;
    }
  }

  .logo-contact-section {
    .logo-section {
      text-align: left;

      .footer-logo {
        margin-left: 0;
        margin-right: auto;
      }

      .contact-info {
        text-align: left;

        .contact-text {
          font-family: "Inter" !important;
          color: #031752;
        }

        .contact-details {
          font-family: "Inter" !important;
        }

        .contact-email {
          color: #000c66;
        }

        .social-icons {
          justify-content: flex-start;
        }
      }
    }
  }
}

// Responsive Design for Newsletter and Footer
@media (max-width: 768px) {
  .newsletter-section {
    .newsletter-card {
      padding: 20px;

      .newsletter-content {
        flex-direction: column;
        gap: 15px;

        .newsletter-text-section {
          flex-direction: row;
          gap: 10px;
          white-space: normal;
          text-align: center;
          justify-content: center;
          align-items: center;
          margin: 0 auto;
          max-width: 100%;

          .email-icon-container {
            flex-shrink: 0;

            .email-icon {
              width: 40px;
              height: 40px;
            }
          }

          .newsletter-title {
            font-size: 1rem;
            white-space: normal;
            flex-shrink: 0;
            margin: 0;
          }

          .newsletter-description {
            font-size: 0.85rem;
            white-space: normal;
          }

          .newsletter-input {
            min-width: 200px;
            width: auto;
            max-width: 200px;
            padding: 10px 12px;
            height: 40px;
            font-size: 0.8rem;
            flex-shrink: 1;

            &::placeholder {
              font-size: 0.7rem;
            }
          }

          .newsletter-btn {
            padding: 10px 15px;
            font-size: 0.8rem;
            flex-shrink: 0;
            white-space: nowrap;
           margin-right: -10px;
            height: 38px;


          }
        }
      }
    }
  }

  .download-app-footer {
    .download-text-section {
      .download-title {
        font-size: 2rem;
        font-weight: bold;
        color: #28a745;
      }
    }

    .footer-links {
      .footer-links-container {
        flex-direction: column;
        gap: 15px;
        margin: 0 !important;
      }
    }

    .app-buttons-section {
      .app-buttons {
        justify-content: center;
        margin: 0 !important;
      }
    }

    .logo-contact-section {
      .logo-section {
        text-align: center;

        .contact-info {
          text-align: center;

          .social-icons {
            justify-content: center;
          }
        }
      }
    }
  }
}

/* إصلاح الـ horizontal scroll في الإنجليزية */
:host-context(html[dir="ltr"]) .home-page,
:host-context(html[lang="en"]) .home-page {
  overflow-x: hidden !important;
  max-width: 100vw !important;
  box-sizing: border-box !important;
}

/* إصلاح الـ container-fluid في الإنجليزية */
:host-context(html[dir="ltr"]) .container-fluid,
:host-context(html[lang="en"]) .container-fluid {
  max-width: 100% !important;
  box-sizing: border-box !important;
  padding-left: 15px !important;
  padding-right: 15px !important;
}

/* Responsive Display Control for Download App Footer */
.download-app-footer {
  .d-none.d-xl-flex {
    display: none !important;

    @media (min-width: 1200px) {
      display: flex !important;
    }
  }

  .d-lg-flex.d-xl-none {
    display: none !important;

    @media (min-width: 1024px) and (max-width: 1199px) {
      display: flex !important;
    }
  }

  .d-lg-none {
    display: block !important;

    @media (min-width: 1024px) {
      display: none !important;
    }
  }
}
@media (max-width: 576px) {
  .exact-filter-bar .bottom-row{
    gap: 0px !important;
  }
}

@media (max-width: 445px) {
  .newsletter-section {


    .newsletter-card {
      .newsletter-content {
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
        justify-content: center !important;
        text-align: center !important;

        .newsletter-text-section {
          flex-direction: column !important;
          align-items: center !important;
          justify-content: center !important;
          text-align: center !important;
          gap: 15px !important;
          padding: 0 20px;
          width: 100% !important;
          max-width: 350px !important;
          margin: 0 auto !important;

          .email-icon-container {
            display: flex !important;
            justify-content: center !important;

            .email-icon {
              width: 50px !important;
              height: 50px !important;
            }
          }

          .newsletter-title {
            font-size: 1.1rem !important;
            text-align: center !important;
            display: flex !important;
            flex-direction: column !important;
            align-items: center !important;
            justify-content: center !important;
            width: 100% !important;

            .green-text {
              display: flex !important;
              justify-content: center !important;
              align-items: center !important;
              text-align: center !important;
              width: 100% !important;
            }
          }

          .newsletter-input {
            min-width: auto !important;
            width: 100% !important;
            max-width: 280px !important;
            padding: 12px 16px !important;
            height: 45px !important;
            font-size: 0.9rem !important;
            text-align: center !important;
            margin: 0 auto !important;
            display: block !important;
          }

          .newsletter-btn {
            width: auto !important;
            padding: 12px 30px !important;
            font-size: 0.9rem !important;
            margin: 0 auto !important;
            display: block !important;
          }
        }
      }
    }
  }
}
@media (max-width:786px) {
  .row.g-4{
    justify-content: center !important;
  }
}

// Footer logo quality improvements
.footer-logo {
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  image-rendering: auto;
  -ms-interpolation-mode: bicubic;
  max-width: 100%;
  height: auto;
  filter: contrast(1.1) brightness(1.05);

  // Additional quality improvements
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);

  // Smooth scaling
  transition: all 0.3s ease;

  // Prevent blurry scaling
  transform-origin: center;
  will-change: transform;
}

// Social icons positioning for download app footer
.download-app-footer .logo-section .contact-info .social-icons {
  margin-right: -7%;
}

// Social icons positioning for 1024px screens only
@media (min-width: 1024px) and (max-width: 1199px) {
  .download-app-footer .logo-section .contact-info .social-icons {
    margin-right: 18%;
  }
}

// Social icons positioning for 1440px screens
@media (min-width: 1440px) {
  .download-app-footer .logo-section .contact-info .social-icons {
    margin-right: 21%;
  }
}

// Property Card Styles - Copied from Home Page
.property-card {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  height: 400px;
  width: 100%;
  max-width: 320px;
  opacity: 1;
  margin: 0 auto;
  cursor: pointer;
  border: 1px solid #f0f0f0;
  font-family: "Markazi Text";
  display: flex;
  flex-direction: column;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  }

  .property-image {
    position: relative;
    width: 100%;
    height: 320px;
    opacity: 1;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    overflow: hidden;
    transform: rotate(0deg);

    &::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 50%;
      background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
      z-index: 1;
      pointer-events: none;
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.3s ease;
    }

    &:hover img {
      transform: scale(1.05);
    }

    // Top Badges Container
    .property-top-badges {
      position: absolute;
      top: 12px;
      right: 12px;
      display: flex;
      align-items: flex-start;
      width: auto;
      height: 24px;
      gap: 4px;
      z-index: 2;
    }

    // English layout - left positioning
    :host-context(html[dir="ltr"]) .property-top-badges,
    :host-context(html[lang="en"]) .property-top-badges {
      left: 12px;
    }

    // English layout - property location left positioning
    :host-context(html[dir="ltr"]) .property-location-text,
    :host-context(html[lang="en"]) .property-location-text {
      left: 12px;
    }

    // English layout - smaller font for Featured badge
    :host-context(html[dir="ltr"]) .property-badge-featured,
    :host-context(html[lang="en"]) .property-badge-featured {
      font-size: 0.7rem !important;
    }

    // Property Type Badge
    // .property-type-badge {
    //   position: absolute;
    //   bottom: 12px;
    //   right: 12px;
    //   background: rgba(255, 255, 255, 0.95);
    //   color: #333;
    //   padding: 4px 8px;
    //   border-radius: 12px;
    //   font-size: 0.75rem;
    //   font-weight: 500;
    //   z-index: 2;
    //   backdrop-filter: blur(10px);
    // }

    // English layout - property type badge left positioning
    :host-context(html[dir="ltr"]) .property-type-badge,
    :host-context(html[lang="en"]) .property-type-badge {
      left: 12px;
      right: auto;
    }

    // Property Title
    .property-title {
      position: absolute;
      bottom: 50px;
      right: 12px;
      left: 12px;
      color: white;
      font-size: 1.4rem;
      font-weight: 600;
      margin: 0;
      z-index: 2;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
      line-height: 1.3;

      .verified-icon {
        color: #28a745;
        margin-left: 5px;
        font-size: 0.9rem;
      }
    }

    // Property Location
    .property-location-text {
      position: absolute;
      bottom: 25px;
      right: 12px;
      color: rgba(255, 255, 255, 0.9);
      font-size: 0.85rem;
      z-index: 2;
      display: flex;
      align-items: center;
      gap: 4px;

      i {
        font-size: 0.8rem;
        color: #ff6b6b;
      }
    }
  }

  // Property Content
  .property-content {
    padding: 12px;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
}

// Property Card Fixed Dimensions for different screen sizes
@media (min-width: 1200px) {
  .property-card {
    width: 100% !important;
    max-width: 320px !important;
    height: 290px !important;
  }

  .property-title {
    font-size: 1.5rem !important;
  }
}

// Large screens (1024px - 1199px)
@media (max-width: 1199px) and (min-width: 1024px) {
  .property-card {
    width: 100% !important;
    max-width: 240px !important;
    height: 270px !important;
  }

  .property-title {
    font-size: 1.4rem !important;
  }
}

// Medium-Large screens (992px - 1023px)
@media (max-width: 1023px) and (min-width: 992px) {
  .property-card {
    width: 100% !important;
    max-width: 250px !important;
    height: 280px !important;
  }

  .property-title {
    font-size: 1.3rem !important;
  }
}

// Tablet screens (768px - 991px)
@media (max-width: 991px) and (min-width: 768px) {
  .property-card {
    width: 100% !important;
    max-width: 220px !important;
    height: 300px !important;
  }

  .property-title {
    font-size: 1.25rem !important;
  }
}

// Mobile screens (≤767px)
@media (max-width: 767px) {
  .property-card {
    width: 100% !important;
    max-width: 280px !important;
    height: 320px !important;
    margin: 0 auto !important;
  }

  .property-title {
    font-size: 1.2rem !important;
  }
}

// Small mobile screens (≤576px)
@media (max-width: 576px) {
  .property-card {
    max-width: 300px !important;
    height: 264px !important;
  }

  .property-title {
    font-size: 1.1rem !important;
  }
}

// Extra small mobile screens (≤480px)
@media (max-width: 480px) {
  .property-card {
    max-width: 280px !important;
    height: 262px !important;
  }

  .property-title {
    font-size: 1.05rem !important;
  }
}

// Very small screens (≤375px)
@media (max-width: 375px) {
  .property-card {
    max-width: 260px !important;
    height: 262px !important;
  }

  .property-title {
    font-size: 1rem !important;
  }
}

// Arabic font support
:host-context(html[dir="rtl"]) .property-card,
:host-context(html[lang="ar"]) .property-card,
:host-context(.rtl) .property-card {
  font-family: "Markazi Text", sans-serif !important;
}

:host-context(html[dir="rtl"]) .property-badge-featured,
:host-context(html[lang="ar"]) .property-badge-featured,
:host-context(.rtl) .property-badge-featured {
  font-family: "Markazi Text", sans-serif !important;
}

:host-context(html[dir="rtl"]) .property-price-overlay,
:host-context(html[lang="ar"]) .property-price-overlay,
:host-context(.rtl) .property-price-overlay {
  font-family: "Markazi Text", sans-serif !important;
}

:host-context(html[dir="rtl"]) .rating-text,
:host-context(html[lang="ar"]) .rating-text,
:host-context(.rtl) .rating-text {
  font-family: "Markazi Text", sans-serif !important;
}

:host-context(html[dir="rtl"]) .reviews-count,
:host-context(html[lang="ar"]) .reviews-count,
:host-context(.rtl) .reviews-count {
  font-family: "Markazi Text", sans-serif !important;
}

// Property title font size override
.property-title {
  font-size: 1.1rem !important;
}
