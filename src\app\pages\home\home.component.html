<div class="home-page">
  <!-- Header Section -->
  <header class="home-header">
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg">
      <div class="container-fluid px-4">
        <!-- Logo -->
        <div class="navbar-brand" (click)="navigateToHome()" style="cursor: pointer;">
          <img alt="Logo" src="./assets/media/easydeallogos/loading-logo.png" class="h-55px app-sidebar-logo-default" />
        </div>

        <!-- Mobile Menu Toggle Button -->
        <button class="navbar-toggler d-lg-none" type="button" (click)="toggleMobileMenu()"
          [attr.aria-expanded]="showMobileMenu" aria-label="Toggle navigation">
          <span class="navbar-toggler-icon"> </span>
        </button>

        <!-- Navigation Menu -->
        <div class="navbar-nav mx-auto d-none d-lg-flex">
          <ul class="nav-list d-flex align-items-center mb-0">
            <li class="nav-item">
              <a href="/home" class="nav-link">{{
                "HOME.NAVIGATION.HOME" | translate
                }}</a>
            </li>
            <li class="nav-item">
              <a href="#" class="nav-link">{{
                "HOME.NAVIGATION.ABOUT_US" | translate
                }}</a>
            </li>

            <li class="nav-item">
              <a href="javascript:void(0)" (click)="scrollToSection()" class="nav-link">
                {{ "HOME.NAVIGATION.ADVERTISEMENTS" | translate }}</a>
            </li>
            <li class="nav-item">
              <a href="#" class="nav-link">{{
                "HOME.NAVIGATION.CONTACT_US" | translate
                }}</a>
            </li>
          </ul>
        </div>

        <!-- Mobile Navigation Dropdown -->
        <div *ngIf="showMobileMenu" class="mobile-nav-dropdown d-lg-none">
          <div class="dropdown-item">
            <span class="menu-icon me-2">
              <i class="fas fa-home fs-6 text-primary"></i>
            </span>
            <span>{{ "HOME.NAVIGATION.HOME" | translate }}</span>
          </div>
          <div class="dropdown-item">
            <span class="menu-icon me-2">
              <i class="fas fa-info-circle fs-6 text-info"></i>
            </span>
            <span>{{ "HOME.NAVIGATION.ABOUT_US" | translate }}</span>
          </div>

          <div class="dropdown-item">
            <span class="menu-icon me-2">
              <i class="fas fa-bullhorn fs-6 text-warning"></i>
            </span>
            <a href="javascript:void(0)" (click)="scrollToSection()" class="nav-link">
              {{ "HOME.NAVIGATION.ADVERTISEMENTS" | translate }}</a>
          </div>
          <div class="dropdown-item">
            <span class="menu-icon me-2">
              <i class="fas fa-phone fs-6 text-gray-600"></i>
            </span>
            <a href="#" class="nav-link">{{
              "HOME.NAVIGATION.CONTACT_US" | translate
              }}</a>
          </div>
          <div class="dropdown-divider"></div>
          <div class="dropdown-item mobile-language-toggle">
            <app-language-toggle></app-language-toggle>
          </div>
        </div>

        <!-- Language Toggle & User Section -->
        <div class="navbar-nav position-relative d-flex align-items-center">
          <!-- Language Toggle Component - Hidden on mobile -->
          <div class="me-3 d-none d-lg-block">
            <app-language-toggle></app-language-toggle>
          </div>
          <!-- If user is logged in, show user profile -->
          <div *ngIf="isLoggedIn" class="nav-link user-profile" (click)="toggleUserDropdown()">
            <img [src]="getUserProfileImage()" [alt]="getUserDisplayName()" class="user-avatar me-2" />
            <span class="user-name">{{ getUserDisplayName() }}</span>
            <i class="fas fa-chevron-down ms-2"></i>
          </div>

          <!-- User Dropdown Menu -->
          <div *ngIf="isLoggedIn && showUserDropdown" class="user-dropdown">
            <!-- Admin Menu Items -->
            <ng-container *ngIf="isAdmin()">
              <!-- Dashboard -->
              <div class="dropdown-item" (click)="navigateToDashboard()">
                <span class="menu-icon me-2">
                  <app-keenicon name="element-11" class="fs-5 text-primary" type="outline"></app-keenicon>
                </span>
                <span>{{ "USER_MENU.DASHBOARD" | translate }}</span>
              </div>

              <!-- Messages -->
              <div class="dropdown-item" (click)="navigateToChat()">
                <span class="menu-icon me-2">
                  <app-keenicon name="messages" class="fs-5 text-info" type="outline"></app-keenicon>
                </span>
                <span>{{ "USER_MENU.MESSAGES" | translate }}</span>
              </div>

              <!-- My Profile -->
              <div class="dropdown-item" (click)="navigateToProfile()">
                <span class="menu-icon me-2">
                  <app-keenicon name="user" class="fs-5 text-primary" type="outline"></app-keenicon>
                </span>
                <span>{{ "USER_MENU.MY_PROFILE" | translate }}</span>
              </div>
              <!-- Logout -->
              <div class="dropdown-divider"></div>
              <div class="dropdown-item logout-item" (click)="logout()">
                <span class="menu-icon me-2">
                  <i class="fas fa-sign-out-alt fs-6 text-danger"></i>
                </span>
                <span>{{ "USER_MENU.LOGOUT" | translate }}</span>
              </div>
            </ng-container>

            <!-- Client Menu Items -->
            <ng-container *ngIf="isClient()">
              <!-- New Request -->
              <div class="dropdown-item new-request-item" (click)="navigateToStepperModal()">
                <span class="text-success">{{
                  "USER_MENU.NEW_REQUEST" | translate
                  }}</span>
              </div>

              <!-- Messages -->
              <div class="dropdown-item" (click)="navigateToChat()">
                <span class="menu-icon me-2">
                  <app-keenicon name="messages" class="fs-5 text-info" type="outline"></app-keenicon>
                </span>
                <span>{{ "USER_MENU.MESSAGES" | translate }}</span>
              </div>

              <!-- My Profile -->
              <div class="dropdown-item" (click)="navigateToProfile()">
                <span class="menu-icon me-2">
                  <app-keenicon name="user" class="fs-5 text-primary" type="outline"></app-keenicon>
                </span>
                <span>{{ "USER_MENU.MY_PROFILE" | translate }}</span>
              </div>

              <!-- Logout -->
              <div class="dropdown-divider"></div>
              <div class="dropdown-item logout-item" (click)="logout()">
                <span class="menu-icon me-2">
                  <i class="fas fa-sign-out-alt fs-6 text-danger"></i>
                </span>
                <span>{{ "USER_MENU.LOGOUT" | translate }}</span>
              </div>
            </ng-container>

            <!-- Developer Menu Items -->
            <ng-container *ngIf="isDeveloper()">
              <!-- Dashboard -->
              <div class="dropdown-item" (click)="navigateToDashboard()">
                <span class="menu-icon me-2">
                  <app-keenicon name="element-11" class="fs-5 text-primary" type="outline"></app-keenicon>
                </span>
                <span>{{ "USER_MENU.DASHBOARD" | translate }}</span>
              </div>

              <!-- Messages -->
              <div class="dropdown-item" (click)="navigateToChat()">
                <span class="menu-icon me-2">
                  <app-keenicon name="messages" class="fs-5 text-info" type="outline"></app-keenicon>
                </span>
                <span>{{ "USER_MENU.MESSAGES" | translate }}</span>
              </div>

              <!-- My Profile -->
              <div class="dropdown-item" (click)="navigateToProfile()">
                <span class="menu-icon me-2">
                  <app-keenicon name="user" class="fs-5 text-primary" type="outline"></app-keenicon>
                </span>
                <span>{{ "USER_MENU.MY_PROFILE" | translate }}</span>
              </div>

              <!-- Logout -->
              <div class="dropdown-divider"></div>
              <div class="dropdown-item logout-item" (click)="logout()">
                <span class="menu-icon me-2">
                  <i class="fas fa-sign-out-alt fs-6 text-danger"></i>
                </span>
                <span>{{ "USER_MENU.LOGOUT" | translate }}</span>
              </div>
            </ng-container>

            <!-- Broker Menu Items -->
            <ng-container *ngIf="isBroker()">
              <!-- Dashboard -->
              <div class="dropdown-item" (click)="navigateToDashboard()">
                <span class="menu-icon me-2">
                  <app-keenicon name="element-11" class="fs-5 text-primary" type="outline"></app-keenicon>
                </span>
                <span>{{ "USER_MENU.DASHBOARD" | translate }}</span>
              </div>

              <!-- Requests -->
              <div class="dropdown-item" (click)="navigateToRequests()">
                <span class="menu-icon me-2">
                  <svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g clip-path="url(#clip0_24_2533)">
                      <path stroke="#e74c3c" stroke-width="1"
                        d="M6.53125 10.0938C7.02313 10.0938 7.42188 9.695 7.42188 9.20312C7.42188 8.71125 7.02313 8.3125 6.53125 8.3125C6.03937 8.3125 5.64062 8.71125 5.64062 9.20312C5.64062 9.695 6.03937 10.0938 6.53125 10.0938Z" />
                      <path stroke="#e74c3c" stroke-width="1"
                        d="M7.125 7.125H5.9375V4.75H7.125C7.43994 4.75 7.74199 4.62489 7.96469 4.40219C8.18739 4.17949 8.3125 3.87744 8.3125 3.5625C8.3125 3.24756 8.18739 2.94551 7.96469 2.72281C7.74199 2.50011 7.43994 2.375 7.125 2.375H5.9375C5.62267 2.37536 5.32083 2.50059 5.09821 2.72321C4.87559 2.94583 4.75036 3.24767 4.75 3.5625V3.85938H3.5625V3.5625C3.56321 2.93283 3.81366 2.32915 4.2589 1.8839C4.70415 1.43866 5.30783 1.18821 5.9375 1.1875H7.125C7.75489 1.1875 8.35898 1.43772 8.80438 1.88312C9.24978 2.32852 9.5 2.93261 9.5 3.5625C9.5 4.19239 9.24978 4.79648 8.80438 5.24188C8.35898 5.68728 7.75489 5.9375 7.125 5.9375V7.125Z" />
                      <path stroke="#e74c3c" stroke-width="1"
                        d="M13.3284 12.4887C13.9224 11.7779 14.3579 10.9487 14.6059 10.0562C14.8539 9.1638 14.9088 8.22873 14.7668 7.31342C14.6248 6.39811 14.2892 5.52361 13.7825 4.74825C13.2758 3.9729 12.6095 3.31453 11.8282 2.81708L11.235 3.84427C12.0092 4.35075 12.6386 5.04962 13.0615 5.87244C13.4844 6.69526 13.6864 7.61381 13.6476 8.53814C13.6089 9.46247 13.3308 10.3609 12.8405 11.1454C12.3502 11.93 11.6645 12.5737 10.8506 13.0136C10.0368 13.4536 9.12262 13.6746 8.19768 13.655C7.27275 13.6355 6.36874 13.376 5.57419 12.9021C4.77965 12.4282 4.1218 11.7561 3.66508 10.9515C3.20836 10.147 2.96841 9.23762 2.96875 8.31247H1.78125C1.7803 9.55369 2.13332 10.7694 2.7989 11.8171C3.46449 12.8648 4.41503 13.7009 5.53904 14.2274C6.66304 14.754 7.91388 14.9491 9.14484 14.7898C10.3758 14.6306 11.5358 14.1236 12.4888 13.3284L16.9729 17.8125L17.8125 16.9728L13.3284 12.4887Z" />
                    </g>
                    <defs>
                      <clipPath id="clip0_24_2533">
                        <rect width="19" height="19" fill="white" />
                      </clipPath>
                    </defs>
                  </svg>
                </span>
                <span>{{ "USER_MENU.REQUESTS" | translate }}</span>
              </div>

              <!-- My Profile -->
              <div class="dropdown-item" (click)="navigateToProfile()">
                <span class="menu-icon me-2">
                  <app-keenicon name="user" class="fs-5 text-primary" type="outline"></app-keenicon>
                </span>
                <span>{{ "USER_MENU.MY_PROFILE" | translate }}</span>
              </div>

              <!-- Messages -->
              <div class="dropdown-item" (click)="navigateToChat()">
                <span class="menu-icon me-2">
                  <app-keenicon name="messages" class="fs-5 text-info" type="outline"></app-keenicon>
                </span>
                <span>{{ "USER_MENU.MESSAGES" | translate }}</span>
              </div>

              <!-- Logout -->
              <div class="dropdown-divider"></div>
              <div class="dropdown-item logout-item" (click)="logout()">
                <span class="menu-icon me-2">
                  <i class="fas fa-sign-out-alt fs-6 text-danger"></i>
                </span>
                <span>{{ "USER_MENU.LOGOUT" | translate }}</span>
              </div>

              <div class="dropdown-divider"></div>

              <!-- New Request -->
              <div class="dropdown-item new-request-item" (click)="navigateToStepperModal()">
                <span class="text-success">{{
                  "USER_MENU.NEW_REQUEST" | translate
                  }}</span>
              </div>

              <div class="dropdown-divider"></div>
            </ng-container>
          </div>

          <!-- If user is not logged in, show register button -->
          <a *ngIf="!isLoggedIn" [routerLink]="['/authentication/login']" class="nav-link user-link">
            <i class="fas fa-user me-2"></i>
            {{ "HOME.NAVIGATION.REGISTER_GUEST" | translate }}
          </a>
        </div>
      </div>
    </nav>

    <!-- Hero Image -->
    <div class="hero-background">
      <img src="./assets/media/home/<USER>" alt="Hero Background" class="hero-bg-image" />
      <div class="hero-overlay"></div>
    </div>
  </header>

  <!-- Request Buttons Section - Show only for Client and Guest -->
  <section class="request-buttons-section" *ngIf="getUserRole() === 'client' || !getUserRole() || !isLoggedIn">
    <div class="container">
      <div class="request-buttons-container">
        <div class="row justify-content-center">
          <div class="col-lg-2 col-md-4 col-sm-6 col-6 mb-3">
            <button class="request-btn" (click)="handleRequestClick('buy-apartment')">
              <i class="fas fa-home"></i>
              <span *ngIf="currentLang === 'ar'">طلب شراء شقة</span>
              <span *ngIf="currentLang === 'en'">Buy Apartment</span>
            </button>
          </div>
          <div class="col-lg-2 col-md-4 col-sm-6 col-6 mb-3">
            <button class="request-btn" (click)="handleRequestClick('rent-apartment')">
              <i class="fas fa-key"></i>
              <span *ngIf="currentLang === 'ar'">طلب تأجير شقة</span>
              <span *ngIf="currentLang === 'en'">Rent Apartment</span>
            </button>
          </div>
          <div class="col-lg-2 col-md-4 col-sm-6 col-6 mb-3">
            <button class="request-btn" (click)="handleRequestClick('buy-villa')">
              <i class="fas fa-building"></i>
              <span *ngIf="currentLang === 'ar'">طلب شراء فيلا</span>
              <span *ngIf="currentLang === 'en'">Buy Villa</span>
            </button>
          </div>
          <div class="col-lg-2 col-md-4 col-sm-6 col-6 mb-3">
            <button class="request-btn" (click)="handleRequestClick('sell-property')">
              <i class="fas fa-money-bill"></i>
              <span *ngIf="currentLang === 'ar'">طلب بيع</span>
              <span *ngIf="currentLang === 'en'">Sell Property</span>
            </button>
          </div>
          <div class="col-lg-2 col-md-4 col-sm-6 col-6 mb-3">
            <button class="request-btn" (click)="handleRequestClick('buy-chalet')">
              <i class="fas fa-tree"></i>
              <span *ngIf="currentLang === 'ar'">طلب شراء شاليه مصيفي</span>
              <span *ngIf="currentLang === 'en'"> Buy Summer Chalet</span>
            </button>
          </div>
          <div class="col-lg-2 col-md-4 col-sm-6 col-6 mb-3">
            <button class="request-btn" (click)="handleRequestClick('rent-duplex')">
              <i class="fas fa-handshake"></i>
              <span *ngIf="currentLang === 'ar'">طلب تاجير دوبلكس</span>
              <span *ngIf="currentLang === 'en'"> Rent Duplex</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Properties Section -->
  <section id="properties-section" class="properties-section">
    <div class="container">
      <div class="row">
        <div class="col-12">
          <h2 class="section-title text-center mb-5">
            {{ "HOME.PROPERTIES.TITLE" | translate }}
          </h2>
        </div>
      </div>

      <div class="row g-4">
        <!-- Dynamic Property Cards -->
        <div class="col-lg-3 col-md-6 col-sm-12" *ngFor="let property of properties; let i = index">
          <div class="property-card" (click)="onPropertyClick(property)" style="cursor: pointer">
            <div class="property-image">
              <img src="{{ property.gallery?.[0]?.url || 'assets/media/home/<USER>/1.jpg' }}"
                [alt]="'Property ' + (i + 1)" class="img-fluid" />

              <!-- Top Badges Container -->
              <div class="property-top-badges">
                <!-- Featured Badge -->
                <div class="property-badge-featured">
                  <i class="fas fa-star"></i>
                  <span *ngIf="currentLang === 'ar'">أعلان مميز </span>
                  <span *ngIf="currentLang === 'en'">Featured Ad</span>
                </div>

                <!-- Price Badge -->
                <div class="property-price-overlay">
                  <i class="fas fa-money-bill"></i>
                  <span class="price-amount">{{
                    getFormattedPrice(property)
                    }}</span>
                </div>
              </div>

              <!-- Property Type - Bottom Left -->
              <div class="property-type-badge">
                <span>{{ getTranslatedPropertyType(property.type) }}</span>
              </div>

              <!-- Property Title -->
              <h4 class="property-title">
                <span *ngIf="property.ground_area || property.building_area || property.unitArea">
                  <span *ngIf="property.ground_area">
                    {{ property.ground_area }} {{ translationService.isRTL() ? "م² أرض" : "m² Ground" }}
                  </span>
                  <span *ngIf="property.building_area && property.ground_area"> - </span>
                  <span *ngIf="property.building_area">
                    {{ property.building_area }} {{ translationService.isRTL() ? "م² مبنى" : "m² Building" }}
                  </span>
                  <span *ngIf="property.unitArea && (property.ground_area || property.building_area)"> - </span>
                  <span *ngIf="property.unitArea">
                    {{ property.unitArea }} {{ translationService.isRTL() ? "م² مساحة" : "m² Unit" }}
                  </span>
                </span>
                <i class="fas fa-check-circle verified-icon"></i>
              </h4>

              <!-- Location -->
              <p class="property-location-text">
                <i class="fas fa-map-marker-alt"></i>
                <span>{{ getAreaName(property) }}</span>
              </p>
            </div>

            <div class="property-content">
              <!-- Rating and Actions Row -->
              <div class="property-bottom-row">
                <div class="property-rating">
                  <div class="stars">
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                  </div>
                  <span class="rating-text">5.0</span>
                  <span class="reviews-count" *ngIf="currentLang === 'ar'">(107 تقييم)</span>
                  <span class="reviews-count" *ngIf="currentLang === 'en'">(107 reviews)</span>
                </div>
                <div class="property-actions">
                  <button class="btn btn-icon">
                    <i class="fas fa-share-alt"></i>
                  </button>
                  <button class="btn btn-icon">
                    <i class="far fa-heart"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Loading State -->
        <div class="col-12 text-center" *ngIf="properties.length === 0">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">{{
              "HOME.PROPERTIES.LOADING" | translate
              }}</span>
          </div>
          <p class="mt-3">{{ "HOME.PROPERTIES.LOADING" | translate }}</p>
        </div>

        <!-- Load More Button -->
        <div class="col-12 text-center" *ngIf="properties.length > 0">
          <button class="btn btn-secondary btn-lg" (click)="loadMoreProperties()">
            {{ "HOME.PROPERTIES.LOAD_MORE" | translate }}
          </button>
        </div>
      </div>
    </div>
  </section>

  <!-- Explore Locations Section -->
  <section class="horizontal-carousel-section">
    <div class="container">
      <div class="row">
        <div class="col-12 p-5">
          <h2 class="section-title text-center mb-5">
            {{ "HOME.LOCATIONS.TITLE" | translate }}
          </h2>
        </div>
      </div>

      <div class="carousel-container position-relative">
        <!-- Bootstrap Carousel -->
        <div id="horizontalCarousel" class="carousel slide" data-bs-ride="carousel" data-bs-interval="5000">
          <div class="carousel-inner">
            <!-- Dynamic Slides -->
            <div class="carousel-item" *ngFor="let slide of locationSlides; let i = index" [class.active]="i === 0">
              <div class="row justify-content-center g-2 g-md-3 mt-5">
                <div class="col-xl-3 col-lg-3 col-md-3 col-sm-6 col-6" *ngFor="let location of slide">
                  <div class="location-card">
                    <img [src]="getRandomAreaImage()" [alt]="location.area.en || location.area.ar" class="img-fluid" />
                    <div class="location-overlay">
                      <div class="location-info">
                        <h5>
                          {{ getLocalizedAreaName(location.area).slice(0, 19) }}
                        </h5>
                        <p>
                          <i class="fas fa-map-marker-alt"></i>
                          {{ location.count || 0 }}
                          {{
                          "HOME.LOCATIONS.PROPERTIES_AVAILABLE" | translate
                          }}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Carousel Controls -->
          <button class="carousel-control-prev" type="button" data-bs-target="#horizontalCarousel" data-bs-slide="prev"
            (click)="prevSlide()">
            <span class="carousel-control-prev-icon" aria-hidden="true"></span>
            <span class="visually-hidden">Previous</span>
          </button>
          <button class="carousel-control-next" type="button" data-bs-target="#horizontalCarousel" data-bs-slide="next"
            (click)="nextSlide()">
            <span class="carousel-control-next-icon" aria-hidden="true"></span>
            <span class="visually-hidden">Next</span>
          </button>
        </div>
      </div>
    </div>
  </section>

  <!-- Articles Section -->
  <section class="articles-section">
    <div class="container">
      <!-- Section Header -->
      <div class="row">
        <div class="col-12">
          <div *ngIf="translationService.isRTL()"
            class="section-header d-flex align-items-center justify-content-between">
            <h1 class="articles-title order-1">
              {{ "HOME.ARTICLES.TITLE" | translate }}
            </h1>
            <div class="left-controls d-flex align-items-center gap-2 order-2">
              <button class="carousel-control-btn prev-btn" type="button" data-bs-target="#articlesCarousel"
                data-bs-slide="prev" (click)="prevArticleSlide()">
                <i class="fas fa-chevron-right"></i>
              </button>
              <button class="carousel-control-btn next-btn ms-2" type="button" data-bs-target="#articlesCarousel"
                data-bs-slide="next" (click)="nextArticleSlide()">
                <i class="fas fa-chevron-left"></i>
              </button>
            </div>
          </div>

          <!-- English Layout: العنوان يسار، الأزرار يمين -->
          <div *ngIf="!translationService.isRTL()"
            class="section-header d-flex align-items-center justify-content-between">
            <h1 class="articles-title order-1">
              {{ "HOME.ARTICLES.TITLE" | translate }}
            </h1>
            <div class="left-controls d-flex align-items-center gap-2 order-2">
              <button class="carousel-control-btn prev-btn" type="button" data-bs-target="#articlesCarousel"
                data-bs-slide="prev" (click)="prevArticleSlide()">
                <i class="fas fa-chevron-left"></i>
              </button>
              <button class="carousel-control-btn next-btn ms-2" type="button" data-bs-target="#articlesCarousel"
                data-bs-slide="next" (click)="nextArticleSlide()">
                <i class="fas fa-chevron-right"></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Bootstrap Carousel for Articles - Full Width -->
      <div class="row">
        <!-- Carousel Full Width -->
        <div class="col-12">
          <div id="articlesCarousel" class="carousel slide" data-bs-ride="carousel" data-bs-interval="6000">
            <div class="carousel-inner">
              <!-- Slide 1 - 4 Images Side by Side -->
              <div class="carousel-item active">
                <div class="row g-3 justify-content-center">
                  <div class="col-lg-3 col-md-6 col-sm-6 col-6">
                    <div class="article-card">
                      <div class="article-image">
                        <img src="./assets/media/home/<USER>/1.jpg" alt="Modern Villa" class="img-fluid" />
                        <div class="article-overlay">
                          <div class="article-content">
                            <h4>
                              {{ "HOME.ARTICLES.ARTICLE_1.TITLE" | translate }}
                            </h4>
                            <p>
                              {{
                              "HOME.ARTICLES.ARTICLE_1.DESCRIPTION"
                              | translate
                              }}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-3 col-md-6 col-sm-6 col-6">
                    <div class="article-card">
                      <div class="article-image">
                        <img src="./assets/media/home/<USER>/2.jpg" alt="Hotel Property" class="img-fluid" />
                        <div class="article-overlay">
                          <div class="article-content">
                            <h4>
                              {{ "HOME.ARTICLES.ARTICLE_2.TITLE" | translate }}
                            </h4>
                            <p>
                              {{
                              "HOME.ARTICLES.ARTICLE_2.DESCRIPTION"
                              | translate
                              }}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-3 col-md-6 col-sm-6 col-6">
                    <div class="article-card">
                      <div class="article-image">
                        <img src="./assets/media/home/<USER>/3.jpg" alt="Villa October" class="img-fluid" />
                        <div class="article-overlay">
                          <div class="article-content">
                            <h4>
                              {{ "HOME.ARTICLES.ARTICLE_3.TITLE" | translate }}
                            </h4>
                            <p>
                              {{
                              "HOME.ARTICLES.ARTICLE_3.DESCRIPTION"
                              | translate
                              }}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-3 col-md-6 col-sm-6 col-6">
                    <div class="article-card">
                      <div class="article-image">
                        <img src="./assets/media/home/<USER>/4.jpg" alt="New Article" class="img-fluid" />
                        <div class="article-overlay">
                          <div class="article-content">
                            <h4>
                              {{ "HOME.ARTICLES.ARTICLE_4.TITLE" | translate }}
                            </h4>
                            <p>
                              {{
                              "HOME.ARTICLES.ARTICLE_4.DESCRIPTION"
                              | translate
                              }}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Slide 2 - 4 Images Side by Side -->
              <div class="carousel-item">
                <div class="row g-3 justify-content-center">
                  <div class="col-lg-3 col-md-6 col-sm-6 col-6">
                    <div class="article-card">
                      <div class="article-image">
                        <img src="./assets/media/home/<USER>/5.jpg" alt="New Cairo Apartment" class="img-fluid" />
                        <div class="article-overlay">
                          <div class="article-content">
                            <h4>
                              {{ "HOME.ARTICLES.ARTICLE_5.TITLE" | translate }}
                            </h4>
                            <p>
                              {{
                              "HOME.ARTICLES.ARTICLE_5.DESCRIPTION"
                              | translate
                              }}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-3 col-md-6 col-sm-6 col-6">
                    <div class="article-card">
                      <div class="article-image">
                        <img src="./assets/media/home/<USER>/6.jpg" alt="North Coast Villa" class="img-fluid" />
                        <div class="article-overlay">
                          <div class="article-content">
                            <h4>
                              {{ "HOME.ARTICLES.ARTICLE_6.TITLE" | translate }}
                            </h4>
                            <p>
                              {{
                              "HOME.ARTICLES.ARTICLE_6.DESCRIPTION"
                              | translate
                              }}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-3 col-md-6 col-sm-6 col-6">
                    <div class="article-card">
                      <div class="article-image">
                        <img src="./assets/media/home/<USER>/7.jpg" alt="Downtown Office" class="img-fluid" />
                        <div class="article-overlay">
                          <div class="article-content">
                            <h4>
                              {{ "HOME.ARTICLES.ARTICLE_7.TITLE" | translate }}
                            </h4>
                            <p>
                              {{
                              "HOME.ARTICLES.ARTICLE_7.DESCRIPTION"
                              | translate
                              }}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-3 col-md-6 col-sm-6 col-6">
                    <div class="article-card">
                      <div class="article-image">
                        <img src="./assets/media/home/<USER>/1.jpg" alt="Luxury Property" class="img-fluid" />
                        <div class="article-overlay">
                          <div class="article-content">
                            <h4>
                              {{ "HOME.ARTICLES.ARTICLE_1.TITLE" | translate }}
                            </h4>
                            <p>
                              {{
                              "HOME.ARTICLES.ARTICLE_1.DESCRIPTION"
                              | translate
                              }}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Slide 3 - 4 Images Side by Side -->
              <div class="carousel-item">
                <div class="row g-3 justify-content-center">
                  <div class="col-lg-3 col-md-6 col-sm-6 col-6">
                    <div class="article-card">
                      <div class="article-image">
                        <img src="./assets/media/home/<USER>/2.jpg" alt="Sheikh Zayed Villa" class="img-fluid" />
                        <div class="article-overlay">
                          <div class="article-content">
                            <h4>
                              {{ "HOME.ARTICLES.ARTICLE_2.TITLE" | translate }}
                            </h4>
                            <p>
                              {{
                              "HOME.ARTICLES.ARTICLE_2.DESCRIPTION"
                              | translate
                              }}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-3 col-md-6 col-sm-6 col-6">
                    <div class="article-card">
                      <div class="article-image">
                        <img src="./assets/media/home/<USER>/3.jpg" alt="Maadi Commercial" class="img-fluid" />
                        <div class="article-overlay">
                          <div class="article-content">
                            <h4>
                              {{ "HOME.ARTICLES.ARTICLE_3.TITLE" | translate }}
                            </h4>
                            <p>
                              {{
                              "HOME.ARTICLES.ARTICLE_3.DESCRIPTION"
                              | translate
                              }}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-3 col-md-6 col-sm-6 col-6">
                    <div class="article-card">
                      <div class="article-image">
                        <img src="./assets/media/home/<USER>/4.jpg" alt="Heliopolis Investment" class="img-fluid" />
                        <div class="article-overlay">
                          <div class="article-content">
                            <h4>
                              {{ "HOME.ARTICLES.ARTICLE_4.TITLE" | translate }}
                            </h4>
                            <p>
                              {{
                              "HOME.ARTICLES.ARTICLE_4.DESCRIPTION"
                              | translate
                              }}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-3 col-md-6 col-sm-6 col-6">
                    <div class="article-card">
                      <div class="article-image">
                        <img src="./assets/media/home/<USER>/5.jpg" alt="Premium Property" class="img-fluid" />
                        <div class="article-overlay">
                          <div class="article-content">
                            <h4>
                              {{ "HOME.ARTICLES.ARTICLE_5.TITLE" | translate }}
                            </h4>
                            <p>
                              {{
                              "HOME.ARTICLES.ARTICLE_5.DESCRIPTION"
                              | translate
                              }}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Carousel Indicators -->
            <div class="carousel-indicators">
              <button type="button" data-bs-target="#articlesCarousel" data-bs-slide-to="0" class="active"></button>
              <button type="button" data-bs-target="#articlesCarousel" data-bs-slide-to="1"></button>
              <button type="button" data-bs-target="#articlesCarousel" data-bs-slide-to="2"></button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Newsletter Section -->
  <section class="newsletter-section py-10" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
    <div class="container">
      <div class="row justify-content-center">
        <div class="col-lg-8 col-md-10">
          <div class="newsletter-card">
            <div class="newsletter-content d-flex align-items-center gap-4">
              <!-- Newsletter Content - All in One Line -->
              <div class="newsletter-text-section d-flex align-items-center gap-3 flex-grow-1">
                <!-- Email Icon -->
                <div class="email-icon-container">
                  <img src="./assets/media/home/<USER>" alt="Email Icon" class="email-icon w-100" />
                </div>

                <!-- Title -->
                <h3 class="newsletter-title mb-0">
                  <span *ngIf="translationService.isRTL()">
                    <span class="green-text d-flex">انضم</span> إلى قائمتنا
                    البريدية
                  </span>
                  <span *ngIf="!translationService.isRTL()">
                    <span class="green-text d-flex">Join</span> Our Newsletter
                  </span>
                </h3>

                <!-- Email Input -->
                <input type="email" class="form-control newsletter-input" [placeholder]="
                    translationService.isRTL()
                      ? ' أدخل بريدك الإلكتروني للحصول علي احدث الاخبار و العروض'
                      : 'Enter your email address'
                  " />

                <!-- Subscribe Button -->
                <button class="btn newsletter-btn" [class.en-style]="!translationService.isRTL()">
                  {{ translationService.isRTL() ? "اشتراك" : "Subscribe" }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Download App Section -->
  <section class="download-app-footer py-4" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
    <div class="container">
      <!-- Desktop Layout (1024px and above) -->
      <div class="row align-items-center d-none d-xl-flex" [class.flex-row-reverse]="!translationService.isRTL()">
        <!-- Logo and Contact Section -->
        <div class="col-lg-3" [class.order-1]="translationService.isRTL()"
          [class.order-3]="!translationService.isRTL()">
          <div class="logo-section text-center text-lg-start">
            <img src="./assets/media/easydeallogos/loading-logo.png" alt="Easy Deal Logo" class="footer-logo mb-3" />
            <div class="contact-info">
              <p class="contact-text mb-2" style="font-size: 14px; color: #333">
                {{
                translationService.isRTL()
                ? "بيت العقار الأول في مصر .. ايزي ديل"
                : "Egypt's First Real Estate House .. Easy Deal"
                }}
              </p>
              <p class="contact-details mb-2" style="font-size: 12px; color: #666">
                {{
                translationService.isRTL()
                ? "حقوق الطبع والنشر محفوظه لعام 2025"
                : "Copyright reserved for 2025"
                }}
              </p>
              <p class="contact-email mb-3" style="font-size: 14px; color: #000c66">
                info&#64;easydeal.com
              </p>

              <!-- Social Icons -->
              <div class="social-icons d-flex gap-2 justify-content-center justify-content-lg-start">
                <i class="fab fa-facebook-f social-icon"></i>
                <i class="fab fa-linkedin-in social-icon"></i>
                <i class="fab fa-twitter social-icon"></i>
                <i class="fab fa-pinterest social-icon"></i>
              </div>
            </div>
          </div>
        </div>

        <!-- Download Text - Center -->
        <div class="col-lg-6 order-2 text-center">
          <h2 class="download-title mb-4" style="font-size: 2rem; color: #00ec42 !important; font-weight: bold">
            {{
            translationService.isRTL()
            ? "حمل التطبيق الالكتروني"
            : "Download the App"
            }}
          </h2>

          <!-- Footer Links -->
          <div class="footer-links d-flex flex-wrap justify-content-center gap-3 mb-4">
            <a class="footer-link active" style="
                color: #000c66;
                font-weight: 500;
                text-decoration: none;
                font-size: 14px;
              ">
              {{ translationService.isRTL() ? "الرئيسية" : "Home" }}
            </a>
            <a class="footer-link" style="
                color: #666;
                font-weight: 400;
                text-decoration: none;
                font-size: 14px;
              ">
              {{
              translationService.isRTL() ? "عن ايزي ديل" : "About Easy Deal"
              }}
            </a>
            <a class="footer-link" style="
                color: #666;
                font-weight: 400;
                text-decoration: none;
                font-size: 14px;
              ">
              {{ translationService.isRTL() ? "مشاريع جديده" : "New Projects" }}
            </a>
            <a class="footer-link" style="
                color: #666;
                font-weight: 400;
                text-decoration: none;
                font-size: 14px;
              ">
              {{ translationService.isRTL() ? "اعلانات" : "Advertisements" }}
            </a>
            <a class="footer-link" style="
                color: #666;
                font-weight: 400;
                text-decoration: none;
                font-size: 14px;
              ">
              {{ translationService.isRTL() ? "تواصل معنا" : "Contact Us" }}
            </a>
            <a class="footer-link" style="
                color: #666;
                font-weight: 400;
                text-decoration: none;
                font-size: 14px;
              ">
              {{
              translationService.isRTL()
              ? "الشروط والاحكام"
              : "Terms and Conditions"
              }}
            </a>
            <a class="footer-link" style="
                color: #666;
                font-weight: 400;
                text-decoration: none;
                font-size: 14px;
              ">
              {{
              translationService.isRTL() ? "سياسة الخصوصية" : "Privacy Policy"
              }}
            </a>
          </div>
        </div>

        <!-- App Store Buttons -->
        <div class="col-lg-3" [class.order-3]="translationService.isRTL()"
          [class.order-1]="!translationService.isRTL()">
          <div
            class="app-buttons d-flex flex-row gap-3 align-items-center justify-content-center justify-content-lg-start">
            <img src="./assets/media/home/<USER>" alt="Download on App Store" class="app-store-btn"
              style="max-width: 150px; cursor: pointer" />
            <img src="./assets/media/home/<USER>" alt="Get it on Google Play" class="google-play-btn"
              style="max-width: 150px; cursor: pointer" />
          </div>
        </div>
      </div>

      <!-- 1024px Layout (same as desktop but smaller) -->
      <div class="row align-items-center d-lg-flex d-xl-none" [class.flex-row-reverse]="!translationService.isRTL()">
        <!-- Logo and Contact Section -->
        <div class="col-lg-3" [class.order-1]="translationService.isRTL()"
          [class.order-3]="!translationService.isRTL()">
          <div class="logo-section text-center text-lg-start">
            <img src="./assets/media/easydeallogos/loading-logo.png" alt="Easy Deal Logo" class="footer-logo mb-3"
              style="max-width: 140px" />
            <div class="contact-info">
              <p class="contact-text mb-2" style="font-size: 12px; color: #333">
                {{
                translationService.isRTL()
                ? "بيت العقار الأول في مصر .. ايزي ديل"
                : "Egypt's First Real Estate House .. Easy Deal"
                }}
              </p>
              <p class="contact-details mb-2" style="font-size: 10px; color: #666">
                {{
                translationService.isRTL()
                ? "حقوق الطبع والنشر محفوظه لعام 2025"
                : "Copyright reserved for 2025"
                }}
              </p>
              <p class="contact-email mb-3" style="font-size: 12px; color: #000c66">
                info&#64;easydeal.com
              </p>

              <!-- Social Icons -->
              <div class="social-icons d-flex gap-2 justify-content-center justify-content-lg-start">
                <i class="fab fa-facebook-f social-icon" style="
                    width: 30px !important;
                    height: 30px !important;
                    font-size: 14px !important;
                  "></i>
                <i class="fab fa-linkedin-in social-icon" style="
                    width: 30px !important;
                    height: 30px !important;
                    font-size: 14px !important;
                  "></i>
                <i class="fab fa-twitter social-icon" style="
                    width: 30px !important;
                    height: 30px !important;
                    font-size: 14px !important;
                  "></i>
                <i class="fab fa-pinterest social-icon" style="
                    width: 30px !important;
                    height: 30px !important;
                    font-size: 14px !important;
                  "></i>
              </div>
            </div>
          </div>
        </div>

        <!-- Download Text - Center -->
        <div class="col-lg-6 order-2 text-center">
          <h2 class="download-title mb-4" style="font-size: 2rem; color: #00ec42 !important; font-weight: bold">
            {{
            translationService.isRTL()
            ? "حمل التطبيق الالكتروني"
            : "Download the App"
            }}
          </h2>

          <!-- Footer Links -->
          <div class="footer-links d-flex flex-wrap justify-content-center gap-2 mb-4">
            <a class="footer-link active" style="
                color: #000c66;
                font-weight: 500;
                text-decoration: none;
                font-size: 12px;
              ">
              {{ translationService.isRTL() ? "الرئيسية" : "Home" }}
            </a>
            <a class="footer-link" style="
                color: #666;
                font-weight: 400;
                text-decoration: none;
                font-size: 12px;
              ">
              {{
              translationService.isRTL() ? "عن ايزي ديل" : "About Easy Deal"
              }}
            </a>
            <a class="footer-link" style="
                color: #666;
                font-weight: 400;
                text-decoration: none;
                font-size: 12px;
              ">
              {{ translationService.isRTL() ? "مشاريع جديده" : "New Projects" }}
            </a>
            <a class="footer-link" style="
                color: #666;
                font-weight: 400;
                text-decoration: none;
                font-size: 12px;
              ">
              {{ translationService.isRTL() ? "اعلانات" : "Advertisements" }}
            </a>
            <a class="footer-link" style="
                color: #666;
                font-weight: 400;
                text-decoration: none;
                font-size: 12px;
              ">
              {{ translationService.isRTL() ? "تواصل معنا" : "Contact Us" }}
            </a>
            <a class="footer-link" style="
                color: #666;
                font-weight: 400;
                text-decoration: none;
                font-size: 12px;
              ">
              {{
              translationService.isRTL()
              ? "الشروط والاحكام"
              : "Terms and Conditions"
              }}
            </a>
            <a class="footer-link" style="
                color: #666;
                font-weight: 400;
                text-decoration: none;
                font-size: 12px;
              ">
              {{
              translationService.isRTL() ? "سياسة الخصوصية" : "Privacy Policy"
              }}
            </a>
          </div>
        </div>

        <!-- App Store Buttons -->
        <div class="col-lg-3" [class.order-3]="translationService.isRTL()"
          [class.order-1]="!translationService.isRTL()">
          <div
            class="app-buttons d-flex flex-row gap-3 align-items-center justify-content-center justify-content-lg-start">
            <img src="./assets/media/home/<USER>" alt="Download on App Store" class="app-store-btn"
              style="max-width: 120px; cursor: pointer" />
            <img src="./assets/media/home/<USER>" alt="Get it on Google Play" class="google-play-btn"
              style="max-width: 120px; cursor: pointer" />
          </div>
        </div>
      </div>

      <!-- Mobile/Tablet Layout (below 1024px) -->
      <div class="d-lg-none">
        <!-- Download Title -->
        <div class="text-center mb-4">
          <h2 class="download-title" style="font-size: 1.5rem; color: #00ec42 !important; font-weight: bold">
            {{
            translationService.isRTL()
            ? "حمل التطبيق الالكتروني"
            : "Download the App"
            }}
          </h2>
        </div>

        <!-- App Store Buttons -->
        <div class="text-center mb-4">
          <div class="app-buttons d-flex flex-column flex-sm-row gap-3 justify-content-center align-items-center">
            <img src="./assets/media/home/<USER>" alt="Download on App Store" class="app-store-btn"
              style="max-width: 140px; cursor: pointer" />
            <img src="./assets/media/home/<USER>" alt="Get it on Google Play" class="google-play-btn"
              style="max-width: 140px; cursor: pointer" />
          </div>
        </div>

        <!-- Logo and Contact -->
        <div class="text-center mb-4">
          <img src="./assets/media/easydeallogos/loading-logo.png" alt="Easy Deal Logo" class="footer-logo mb-3"
            style="max-width: 100px" />
          <div class="contact-info">
            <p class="contact-text mb-2" style="font-size: 13px; color: #333">
              {{
              translationService.isRTL()
              ? "بيت العقار الأول في مصر .. ايزي ديل"
              : "Egypt's First Real Estate House .. Easy Deal"
              }}
            </p>
            <p class="contact-details mb-2" style="font-size: 11px; color: #666">
              {{
              translationService.isRTL()
              ? "حقوق الطبع والنشر محفوظه لعام 2025"
              : "Copyright reserved for 2025"
              }}
            </p>
            <p class="contact-email mb-3" style="font-size: 13px; color: #000c66">
              info&#64;easydeal.com
            </p>

            <!-- Social Icons -->
            <div class="social-icons d-flex gap-2 justify-content-center mb-4">
              <i class="fab fa-facebook-f social-icon"></i>
              <i class="fab fa-linkedin-in social-icon"></i>
              <i class="fab fa-twitter social-icon"></i>
              <i class="fab fa-pinterest social-icon"></i>
            </div>
          </div>
        </div>

        <!-- Footer Links -->
        <div class="footer-links text-center">
          <div class="d-flex flex-wrap justify-content-center gap-2 gap-sm-3">
            <a class="footer-link active" style="
                color: #000c66;
                font-weight: 500;
                text-decoration: none;
                font-size: 12px;
              ">
              {{ translationService.isRTL() ? "الرئيسية" : "Home" }}
            </a>
            <a class="footer-link" style="
                color: #666;
                font-weight: 400;
                text-decoration: none;
                font-size: 12px;
              ">
              {{
              translationService.isRTL() ? "عن ايزي ديل" : "About Easy Deal"
              }}
            </a>
            <a class="footer-link" style="
                color: #666;
                font-weight: 400;
                text-decoration: none;
                font-size: 12px;
              ">
              {{ translationService.isRTL() ? "مشاريع جديده" : "New Projects" }}
            </a>
            <a class="footer-link" style="
                color: #666;
                font-weight: 400;
                text-decoration: none;
                font-size: 12px;
              ">
              {{ translationService.isRTL() ? "اعلانات" : "Advertisements" }}
            </a>
            <a class="footer-link" style="
                color: #666;
                font-weight: 400;
                text-decoration: none;
                font-size: 12px;
              ">
              {{ translationService.isRTL() ? "تواصل معنا" : "Contact Us" }}
            </a>
            <a class="footer-link" style="
                color: #666;
                font-weight: 400;
                text-decoration: none;
                font-size: 12px;
              ">
              {{
              translationService.isRTL()
              ? "الشروط والاحكام"
              : "Terms and Conditions"
              }}
            </a>
            <a class="footer-link" style="
                color: #666;
                font-weight: 400;
                text-decoration: none;
                font-size: 12px;
              ">
              {{
              translationService.isRTL() ? "سياسة الخصوصية" : "Privacy Policy"
              }}
            </a>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Back to Top Button -->
  <button class="btn btn-primary back-to-top" (click)="scrollToTop()">
    <i class="fas fa-arrow-up"></i>
  </button>
</div>