<!-- profile details -->
<div class="card mb-5 mb-xl-10">
  <div class="card-header border-0 bg-light-dark-blue" role="button" data-bs-toggle="collapse"
    data-bs-target="#kt_account_profile_details" aria-expanded="true" aria-controls="kt_account_profile_details">
    <div class="card-title m-0">
      <h3 class="fw-bolder m-0">{{ 'PROFILE.PROFILE_DETAILS.TITLE' | translate }}</h3>
    </div>
  </div>
  <div id="kt_account_profile_details" class="collapse show">
    <form #profileForm="ngForm" (ngSubmit)="onSubmit()">
      <div class="card-body border-top p-9">
        <!-- Full Name -->
        <div class="row mb-6">
          <label class="col-lg-2 col-form-label required fw-bold fs-6">{{ 'PROFILE.PROFILE_DETAILS.FULL_NAME' |
            translate }}</label>
          <div class="col-lg-10 fv-row">
            <input type="text" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0 custom-width"
              [placeholder]="'PROFILE.PROFILE_DETAILS.FULL_NAME_PLACEHOLDER' | translate" name="fullName"
              [(ngModel)]="user.fullName" [disabled]="isLoading" required #fullName="ngModel" />
            <div *ngIf="fullName.invalid && (fullName.dirty || fullName.touched)" class="text-danger mt-2">
              <div *ngIf="fullName.errors?.['required']">
                {{ 'PROFILE.PROFILE_DETAILS.FULL_NAME_REQUIRED' | translate }}
              </div>
            </div>
          </div>
        </div>

        <!-- Phone Number -->
        <div class="row mb-6">
          <label class="col-lg-2 col-form-label fw-bold fs-6">
            <span class="required">{{ 'PROFILE.PROFILE_DETAILS.PHONE_NUMBER' | translate }}</span>
          </label>
          <div class="col-lg-10 fv-row">
            <input type="tel" class="form-control form-control-lg form-control-solid custom-width "
              [placeholder]="'PROFILE.PROFILE_DETAILS.PHONE_NUMBER_PLACEHOLDER' | translate" name="phoneNumber"
              [(ngModel)]="user.phone" [disabled]="isLoading" required #phoneNumber="ngModel"
              pattern="^01[0-2,5]{1}[0-9]{8}$" minlength="11" maxlength="11" />
            <div *ngIf="
                phoneNumber.invalid &&
                (phoneNumber.dirty || phoneNumber.touched)
              " class="text-danger mt-2">
              <div *ngIf="phoneNumber.errors?.['required']">
                {{ 'PROFILE.PROFILE_DETAILS.PHONE_NUMBER_REQUIRED' | translate }}
              </div>
              <div *ngIf="phoneNumber.errors?.['pattern']">
                {{ 'PROFILE.PROFILE_DETAILS.PHONE_NUMBER_PATTERN' | translate }}
              </div>
              <div *ngIf="phoneNumber.errors?.['minlength'] || phoneNumber.errors?.['maxlength']">
                {{ 'PROFILE.PROFILE_DETAILS.PHONE_NUMBER_LENGTH' | translate }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="card-footer d-flex justify-content-end py-6 px-9 save-button">
        <button type="submit" class="btn btn-dark-blue btn-active-light-dark-blue" [disabled]="phoneNumber.invalid">
          <ng-container>{{ 'PROFILE.PROFILE_DETAILS.SAVE_CHANGES' | translate }}</ng-container>
          <ng-container *ngIf="localLoading">
            <!-- <span class="indicator-progress" [style.display]="'block'">
              Please wait...{{ " " }}
              <span
                class="spinner-border spinner-border-sm align-middle ms-2"
              ></span>
            </span> -->
          </ng-container>
        </button>
      </div>
    </form>
  </div>
</div>
